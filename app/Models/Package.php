<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'price',
        'duration_days',
        'is_featured',
        'is_premium',
        'is_active',
    ];

    /**
     * Get the ads for the package.
     */
    public function ads()
    {
        return $this->hasMany(Ad::class);
    }
}
