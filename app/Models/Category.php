<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'icon',
        'image',
        'is_featured',
        'order',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function ads()
    {
        return $this->hasMany(Ad::class);
    }

    public function allAds()
    {
        return $this->hasManyThrough(Ad::class, Category::class, 'parent_id', 'category_id', 'id', 'id');
    }

    /**
     * Get all descendant categories (recursive children)
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get all ancestor categories (recursive parents)
     */
    public function ancestors()
    {
        return $this->parent()->with('ancestors');
    }

    /**
     * Check if category has children
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Get all ads from this category and its descendants
     */
    public function getAllAds()
    {
        $categoryIds = $this->descendants()->pluck('id')->push($this->id);
        return Ad::whereIn('category_id', $categoryIds);
    }

    /**
     * Get the URL for the category
     */
    public function getUrlAttribute()
    {
        return route('category.show', $this->slug);
    }

    /**
     * Get featured categories
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get root categories (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc');
    }
}
