<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Payment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Zarinpal\Zarinpal;

class PaymentCreate extends Component
{
    public Ad $ad;

    public function mount(Ad $ad)
    {
        $this->ad = $ad;
    }

    public function pay()
    {
        if (!$this->ad->package || $this->ad->package->price <= 0) {
            session()->flash('error', 'این آگهی نیازی به پرداخت ندارد.');
            return redirect()->route('ads.show', $this->ad);
        }

        // Create a payment record
        $payment = Payment::create([
            'user_id' => Auth::id(),
            'ad_id' => $this->ad->id,
            'amount' => $this->ad->package->price,
            'status' => 'pending',
            'description' => 'پرداخت هزینه آگهی ' . $this->ad->title,
        ]);

        // Initialize Zarinpal
        $zarinpal = new Zarinpal(config('services.zarinpal.merchant_id'));
        $zarinpal->enableSandbox(); // Remove this line in production

        // Send payment request
        $results = $zarinpal->request(
            route('payment.callback'),
            $payment->amount,
            $payment->description,
            Auth::user()->email,
            Auth::user()->mobile
        );

        if ($results['Status'] == 100) {
            // Update payment with transaction ID
            $payment->update([
                'transaction_id' => $results['Authority'],
            ]);

            // Redirect to Zarinpal
            return redirect()->to('https://sandbox.zarinpal.com/pg/StartPay/' . $results['Authority']);
        } else {
            session()->flash('error', 'خطا در اتصال به درگاه پرداخت: ' . $results['Status']);
            return redirect()->route('ads.show', $this->ad);
        }
    }

    public function render()
    {
        return view('livewire.payment-create');
    }
}
