<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UserDashboard extends Component
{
    public $totalAds;
    public $activeAds;
    public $payments;
    public $latestAds;
    public $latestPayments;

    public function mount()
    {
        $user = Auth::user();
        
        $this->totalAds = $user->ads()->count();
        $this->activeAds = $user->ads()->where(function($query) {
            $query->where('expires_at', '>', Carbon::now())
                  ->orWhereNull('expires_at');
        })->count();
        
        $this->payments = $user->payments()->where('status', 'completed')->count();
        
        $this->latestAds = $user->ads()->with('category')->latest()->take(5)->get();
        $this->latestPayments = $user->payments()->with('ad')->latest()->take(5)->get();
    }

    public function renewAd($adId)
    {
        $ad = Ad::findOrFail($adId);
        
        // Check if the ad belongs to the current user
        if ($ad->user_id !== Auth::id()) {
            session()->flash('error', 'شما اجازه تمدید این آگهی را ندارید.');
            return;
        }
        
        // If the ad has a package, redirect to payment
        if ($ad->package_id) {
            return redirect()->route('payment.create', ['ad' => $ad->id]);
        } else {
            // For free ads, just extend the expiration date
            $ad->update([
                'expires_at' => Carbon::now()->addDays(30),
            ]);
            
            session()->flash('success', 'آگهی شما با موفقیت تمدید شد.');
        }
    }

    public function render()
    {
        return view('livewire.user-dashboard');
    }
}
