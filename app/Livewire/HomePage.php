<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Category;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.livewire')]
class HomePage extends Component
{
    public function render()
    {
        $featuredAds = Ad::where('is_featured', true)
            ->where('expires_at', '>', now())
            ->with(['category', 'images'])
            ->latest()
            ->take(6)
            ->get();

        $latestAds = Ad::where('expires_at', '>', now())
            ->with(['category', 'images'])
            ->latest()
            ->take(8)
            ->get();

        $categories = Category::all()->map(function ($category) {
            $category->ads_count = $category->getAllAdsCount();
            return $category;
        });

        return view('livewire.home-page', [
            'featuredAds' => $featuredAds,
            'latestAds' => $latestAds,
            'categories' => $categories,
        ]);
    }
}
