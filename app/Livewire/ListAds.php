<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Category;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.livewire')]
class ListAds extends Component
{
    use WithPagination;

    public $search = '';
    public $categoryFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 9;

    protected $queryString = [
        'search' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->categoryFilter = '';
        $this->sortField = 'created_at';
        $this->sortDirection = 'desc';
        $this->resetPage();
    }

    public function render()
    {
        $query = Ad::query()
            ->with(['category', 'user'])
            ->where(function ($query) {
                $query->where('expires_at', '>', now())
                    ->orWhereNull('expires_at');
            });

        if ($this->search) {
            $query->where(function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('location', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->categoryFilter) {
            $query->where('category_id', $this->categoryFilter);
        }

        // First get premium ads
        $premiumAds = (clone $query)->where('is_premium', true)
            ->orderBy($this->sortField, $this->sortDirection)
            ->take(3)
            ->get();

        // Then get featured ads
        $featuredAds = (clone $query)->where('is_featured', true)
            ->where('is_premium', false)
            ->orderBy($this->sortField, $this->sortDirection)
            ->take(6)
            ->get();

        // Get remaining ads
        $regularQuery = (clone $query)->where('is_premium', false)
            ->where('is_featured', false)
            ->orderBy($this->sortField, $this->sortDirection);

        // Combine all ads
        $premiumIds = $premiumAds->pluck('id')->toArray();
        $featuredIds = $featuredAds->pluck('id')->toArray();
        $excludeIds = array_merge($premiumIds, $featuredIds);

        if (!empty($excludeIds)) {
            $regularQuery->whereNotIn('id', $excludeIds);
        }

        $regularAds = $regularQuery->paginate($this->perPage);

        // Merge collections for the first page
        if ($this->getPage() == 1) {
            $mergedAds = $premiumAds->concat($featuredAds)->concat($regularAds->getCollection());
            $regularAds->setCollection($mergedAds);
        }

        // Get statistics
        $totalAds = Ad::where(function ($query) {
            $query->where('expires_at', '>', now())
                ->orWhereNull('expires_at');
        })->count();

        $totalFeaturedAds = Ad::where('is_featured', true)
            ->where(function ($query) {
                $query->where('expires_at', '>', now())
                    ->orWhereNull('expires_at');
            })->count();

        $totalPremiumAds = Ad::where('is_premium', true)
            ->where(function ($query) {
                $query->where('expires_at', '>', now())
                    ->orWhereNull('expires_at');
            })->count();

        // Get categories with ad counts
        $categories = Category::all()->map(function ($category) {
            $category->ads_count = $category->getAllAdsCount();
            return $category;
        })->sortByDesc('ads_count');

        return view('livewire.list-ads', [
            'ads' => $regularAds,
            'categories' => $categories,
            'totalAds' => $totalAds,
            'featuredAds' => $totalFeaturedAds,
            'premiumAds' => $totalPremiumAds,
            'totalCategories' => $categories->count(),
        ]);
    }
}
