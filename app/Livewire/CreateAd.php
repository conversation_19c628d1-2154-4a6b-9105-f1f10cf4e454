<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\AdImage;
use App\Models\Category;
use App\Models\Package;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;

#[Layout('layouts.livewire')]
class CreateAd extends Component
{
    use WithFileUploads;

    public $title;
    public $description;
    public $price;
    public $location;
    public $category_id;
    public $package_id;
    public $images = [];
    public $primaryImage = 0;
    public $parentCategories = [];

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string',
        'price' => 'required|numeric|min:0',
        'location' => 'required|string|max:255',
        'category_id' => 'required|exists:categories,id',
        'package_id' => 'nullable|exists:packages,id',
        'images' => 'required|array|min:1|max:5',
        'images.*' => 'image|max:2048', // 2MB Max
        'primaryImage' => 'required|numeric',
    ];

    public function mount()
    {
        // Get parent categories with their children
        $this->parentCategories = Category::with('children')
            ->whereNull('parent_id')
            ->orderBy('order')
            ->get();
    }

    public function removeImage($index)
    {
        // Remove image at the specified index
        if (isset($this->images[$index])) {
            unset($this->images[$index]);
            $this->images = array_values($this->images); // Re-index array

            // If the primary image was removed, set the first image as primary
            if ($this->primaryImage == $index && count($this->images) > 0) {
                $this->primaryImage = 0;
            } elseif ($this->primaryImage > $index) {
                // Adjust primary image index if it was after the removed image
                $this->primaryImage--;
            }
        }
    }

    public function save()
    {
        $this->validate();

        $package = null;
        $expiresAt = null;
        $type = 'basic';
        $isFeatured = false;
        $isPremium = false;

        if ($this->package_id) {
            $package = Package::find($this->package_id);
            $expiresAt = Carbon::now()->addDays($package->duration_days);
            $isFeatured = $package->is_featured;
            $isPremium = $package->is_premium;

            if ($isFeatured && $isPremium) {
                $type = 'premium';
            } elseif ($isFeatured) {
                $type = 'featured';
            } elseif ($package->name === 'نردبانی') {
                $type = 'ladder';
            }
        } else {
            // Free ad expires after 30 days
            $expiresAt = Carbon::now()->addDays(30);
        }

        // Create the ad
        $ad = Ad::create([
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'location' => $this->location,
            'category_id' => $this->category_id,
            'user_id' => Auth::id(),
            'package_id' => $this->package_id,
            'type' => $type,
            'is_featured' => $isFeatured,
            'is_premium' => $isPremium,
            'expires_at' => $expiresAt,
        ]);

        // Save images
        foreach ($this->images as $index => $image) {
            $imagePath = $image->store('ads', 'public');

            AdImage::create([
                'ad_id' => $ad->id,
                'image_path' => $imagePath,
                'is_primary' => ($index == $this->primaryImage),
                'order' => $index,
            ]);
        }

        // Redirect to payment if needed
        if ($package && $package->price > 0) {
            return redirect()->route('payment.create', ['ad' => $ad->id]);
        }

        session()->flash('success', 'آگهی شما با موفقیت ثبت شد.');
        return redirect()->route('ads.show', $ad);
    }

    public function render()
    {
        return view('livewire.create-ad', [
            'packages' => Package::where('is_active', true)->get(),
        ]);
    }
}
