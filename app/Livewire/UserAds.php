<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class UserAds extends Component
{
    use WithPagination;
    use AuthorizesRequests;

    public $search = '';
    public $statusFilter = '';
    public $categoryFilter = '';
    public $perPage = 10;

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function renewAd($adId)
    {
        $ad = Ad::findOrFail($adId);
        
        // Check if the ad belongs to the current user
        if ($ad->user_id !== Auth::id()) {
            session()->flash('error', 'شما اجازه تمدید این آگهی را ندارید.');
            return;
        }
        
        // If the ad has a package, redirect to payment
        if ($ad->package_id) {
            return redirect()->route('payment.create', ['ad' => $ad->id]);
        } else {
            // For free ads, just extend the expiration date
            $ad->update([
                'expires_at' => Carbon::now()->addDays(30),
            ]);
            
            session()->flash('success', 'آگهی شما با موفقیت تمدید شد.');
        }
    }

    public function deleteAd($adId)
    {
        $ad = Ad::findOrFail($adId);
        
        $this->authorize('delete', $ad);
        
        $ad->delete();
        
        session()->flash('success', 'آگهی با موفقیت حذف شد.');
    }

    public function render()
    {
        $query = Auth::user()->ads()->with('category');
        
        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }
        
        if ($this->statusFilter === 'active') {
            $query->where(function($q) {
                $q->where('expires_at', '>', Carbon::now())
                  ->orWhereNull('expires_at');
            });
        } elseif ($this->statusFilter === 'expired') {
            $query->where('expires_at', '<', Carbon::now());
        }
        
        if ($this->categoryFilter) {
            $query->where('category_id', $this->categoryFilter);
        }
        
        $ads = $query->latest()->paginate($this->perPage);
        
        return view('livewire.user-ads', [
            'ads' => $ads,
            'categories' => Category::all(),
        ]);
    }
}
