<?php

namespace App\Livewire;

use App\Models\Category;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.livewire')]
class CategoriesIndex extends Component
{
    public $featuredCategories;
    public $mainCategories;

    public function mount()
    {
        // Get featured categories with ad count (including subcategories)
        $this->featuredCategories = Category::where('is_featured', true)
            ->orderBy('order')
            ->get()
            ->map(function ($category) {
                $category->ads_count = $category->getAllAdsCount();
                return $category;
            });

        // Get main categories (root categories) with their children and ad count
        $this->mainCategories = Category::with(['children' => function ($query) {
                $query->orderBy('order');
            }])
            ->whereNull('parent_id')
            ->orderBy('order')
            ->get()
            ->map(function ($category) {
                // Set ads count for main category (including all subcategories)
                $category->ads_count = $category->getAllAdsCount();

                // Set ads count for each child category
                $category->children->map(function ($child) {
                    $child->ads_count = $child->getAllAdsCount();
                    return $child;
                });

                return $category;
            });
    }

    public function render()
    {
        return view('livewire.categories-index');
    }
}
