<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use Auth;
use Hash;

class Login extends Component
{
    public $name;
    public $mobile = '09902520250';
    public $email;
    public $password = '123456789';
    public $password_confirmation = '123456789';

    public function render()
    {
        return view('livewire.login');
    }

    public function login()
    {
        $this->validate(
            [
                'mobile' => 'required|string|max:255',
                'password' => 'required|string|min:8',
            ],
            [
                'mobile.required' => 'لطفا شماره همراه خود را وارد کنید.',
                'mobile.max' => 'شماره همراه نباید بیشتر از 255 کاراکتر باشد.',
                'password.required' => 'لطفا رمز عبور خود را وارد کنید.',
                'password.min' => 'رمز عبور حداقل هشت کاراکتر باشد',
            ],
        );

        // login user
        $user = User::where('mobile', $this->mobile)->first();

        if (!empty($user)) {
            // login exist user
            if (Hash::check($this->password, $user->password)) {
                Auth::loginUsingId($user->id);
                session()->flash('success', 'به سایت خودتون خوش آمدید');
                return redirect()->route('home');
            } else {
                session()->flash('error', 'اطلاعات ورود صحیح نیستند');
                return redirect()->route('login');
            }
        } else {
            session()->flash('error', 'هنوز کاربری با این شماره همراه نداریم / لطفا ابتدا ثبت نام کنید');
            return redirect()->route('login');
        }
    }
}
