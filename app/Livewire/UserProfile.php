<?php

namespace App\Livewire;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class UserProfile extends Component
{
    use WithFileUploads;

    public User $user;
    public $name;
    public $email;
    public $mobile;
    public $avatar;
    public $current_password;
    public $password;
    public $password_confirmation;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'avatar' => 'nullable|image|max:1024', // 1MB Max
        'current_password' => 'nullable|required_with:password',
        'password' => 'nullable|min:8|confirmed',
        'password_confirmation' => 'nullable|required_with:password',
    ];

    public function mount()
    {
        $this->user = Auth::user();
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->mobile = $this->user->mobile;
    }

    public function updateProfile()
    {
        $this->validate();

        // Validate current password if trying to change password
        if ($this->password && !Hash::check($this->current_password, $this->user->password)) {
            $this->addError('current_password', 'رمز عبور فعلی صحیح نیست.');
            return;
        }

        // Update email if changed and not already taken
        if ($this->email !== $this->user->email) {
            $this->validate([
                'email' => 'unique:users,email,' . $this->user->id,
            ]);
        }

        // Handle avatar upload
        if ($this->avatar) {
            // Delete old avatar if exists
            if ($this->user->avatar && Storage::disk('public')->exists($this->user->avatar)) {
                Storage::disk('public')->delete($this->user->avatar);
            }

            // Store new avatar
            $avatarPath = $this->avatar->store('avatars', 'public');
            $this->user->avatar = $avatarPath;
        }

        // Update user data
        $this->user->name = $this->name;
        $this->user->email = $this->email;

        // Update password if provided
        if ($this->password) {
            $this->user->password = Hash::make($this->password);
        }

        $this->user->save();

        // Reset password fields
        $this->reset(['current_password', 'password', 'password_confirmation']);

        session()->flash('success', 'پروفایل شما با موفقیت بروزرسانی شد.');
    }

    public function render()
    {
        return view('livewire.user-profile');
    }
}
