<?php

namespace App\Livewire\Admin\Categories;

use App\Models\Category;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;

class Edit extends Component
{
    use WithFileUploads;

    public Category $category;
    public $name;
    public $slug;
    public $description;
    public $parent_id;
    public $icon;
    public $image;
    public $is_featured;
    public $order;
    public $meta_title;
    public $meta_description;
    public $meta_keywords;
    public $parentCategories = [];

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:categories,slug,' . $this->category->id,
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'icon' => 'nullable|string|max:50',
            'image' => 'nullable|image|max:2048', // 2MB Max
            'is_featured' => 'boolean',
            'order' => 'integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
        ];
    }

    public function mount(Category $category)
    {
        $this->category = $category;
        $this->category->loadCount('ads');
        $this->category->load('children');
        
        $this->name = $category->name;
        $this->slug = $category->slug;
        $this->description = $category->description;
        $this->parent_id = $category->parent_id;
        $this->icon = $category->icon;
        $this->is_featured = $category->is_featured;
        $this->order = $category->order;
        $this->meta_title = $category->meta_title;
        $this->meta_description = $category->meta_description;
        $this->meta_keywords = $category->meta_keywords;
        
        $this->loadParentCategories();
    }

    public function loadParentCategories()
    {
        // Get all root categories except this one and its descendants
        $this->parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $this->category->id)
            ->whereNotIn('id', $this->getDescendantIds($this->category))
            ->orderBy('name')
            ->get();
    }

    private function getDescendantIds(Category $category)
    {
        $ids = [];
        foreach ($category->children as $child) {
            $ids[] = $child->id;
            $ids = array_merge($ids, $this->getDescendantIds($child));
        }
        return $ids;
    }

    public function generateSlug()
    {
        if ($this->name) {
            $this->slug = Str::slug($this->name);
        }
    }

    public function removeImage()
    {
        if ($this->category->image && Storage::disk('public')->exists($this->category->image)) {
            Storage::disk('public')->delete($this->category->image);
        }
        
        $this->category->update(['image' => null]);
        $this->category->refresh();
    }

    public function save()
    {
        $this->validate();

        $categoryData = [
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'parent_id' => $this->parent_id ?: null,
            'icon' => $this->icon,
            'is_featured' => $this->is_featured,
            'order' => $this->order,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
        ];

        // Handle image upload
        if ($this->image) {
            // Delete old image if exists
            if ($this->category->image && Storage::disk('public')->exists($this->category->image)) {
                Storage::disk('public')->delete($this->category->image);
            }
            
            $imagePath = $this->image->store('categories', 'public');
            $categoryData['image'] = $imagePath;
        }

        $this->category->update($categoryData);

        session()->flash('success', 'دسته‌بندی با موفقیت بروزرسانی شد.');
        return redirect()->route('admin.categories.index');
    }

    public function render()
    {
        return view('livewire.admin.categories.edit')
            ->layout('layouts.admin');
    }
}
