<?php

namespace App\Livewire\Admin\Categories;

use App\Models\Category;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use WithFileUploads;

    public $name;
    public $slug;
    public $description;
    public $parent_id;
    public $icon;
    public $image;
    public $is_featured = false;
    public $order = 0;
    public $meta_title;
    public $meta_description;
    public $meta_keywords;
    public $parentCategories = [];

    protected $rules = [
        'name' => 'required|string|max:255',
        'slug' => 'required|string|max:255|unique:categories,slug',
        'description' => 'nullable|string',
        'parent_id' => 'nullable|exists:categories,id',
        'icon' => 'nullable|string|max:50',
        'image' => 'nullable|image|max:2048', // 2MB Max
        'is_featured' => 'boolean',
        'order' => 'integer|min:0',
        'meta_title' => 'nullable|string|max:255',
        'meta_description' => 'nullable|string',
        'meta_keywords' => 'nullable|string|max:255',
    ];

    public function mount()
    {
        $this->loadParentCategories();
    }

    public function loadParentCategories()
    {
        $this->parentCategories = Category::whereNull('parent_id')
            ->orderBy('name')
            ->get();
    }

    public function generateSlug()
    {
        if ($this->name) {
            $this->slug = Str::slug($this->name);
        }
    }

    public function save()
    {
        $this->validate();

        $categoryData = [
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'parent_id' => $this->parent_id ?: null,
            'icon' => $this->icon,
            'is_featured' => $this->is_featured,
            'order' => $this->order,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
        ];

        // Handle image upload
        if ($this->image) {
            $imagePath = $this->image->store('categories', 'public');
            $categoryData['image'] = $imagePath;
        }

        Category::create($categoryData);

        session()->flash('success', 'دسته‌بندی با موفقیت ایجاد شد.');
        return redirect()->route('admin.categories.index');
    }

    public function render()
    {
        return view('livewire.admin.categories.create')
            ->layout('layouts.admin');
    }
}
