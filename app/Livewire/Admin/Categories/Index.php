<?php

namespace App\Livewire\Admin\Categories;

use App\Models\Category;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $parentFilter = '';
    public $featuredFilter = '';
    public $perPage = 10;
    public $categoryToDelete = null;
    public $parentCategories = [];

    protected $queryString = [
        'search' => ['except' => ''],
        'parentFilter' => ['except' => ''],
        'featuredFilter' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];

    public function mount()
    {
        $this->loadParentCategories();
    }

    public function loadParentCategories()
    {
        $this->parentCategories = Category::whereNull('parent_id')
            ->orderBy('name')
            ->get();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingParentFilter()
    {
        $this->resetPage();
    }

    public function updatingFeaturedFilter()
    {
        $this->resetPage();
    }

    public function confirmDelete($categoryId)
    {
        $this->categoryToDelete = Category::with(['children', 'ads'])->find($categoryId);
        $this->dispatch('showDeleteModal');
    }

    public function deleteCategory()
    {
        if ($this->categoryToDelete) {
            // Delete the category
            $this->categoryToDelete->delete();
            
            session()->flash('success', 'دسته‌بندی با موفقیت حذف شد.');
            $this->dispatch('hideDeleteModal');
            $this->categoryToDelete = null;
        }
    }

    public function render()
    {
        $query = Category::withCount('ads')
            ->with('parent');
        
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('slug', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }
        
        if ($this->parentFilter === 'root') {
            $query->whereNull('parent_id');
        } elseif ($this->parentFilter) {
            $query->where('parent_id', $this->parentFilter);
        }
        
        if ($this->featuredFilter !== '') {
            $query->where('is_featured', $this->featuredFilter);
        }
        
        $categories = $query->orderBy('order')
            ->orderBy('name')
            ->paginate($this->perPage);
        
        return view('livewire.admin.categories.index', [
            'categories' => $categories,
        ])->layout('layouts.admin');
    }
}
