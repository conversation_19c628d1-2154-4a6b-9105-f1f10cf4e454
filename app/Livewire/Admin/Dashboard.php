<?php

namespace App\Livewire\Admin;

use App\Models\Ad;
use App\Models\Category;
use App\Models\Payment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $totalAds;
    public $totalUsers;
    public $totalCategories;
    public $totalRevenue;
    public $latestAds;
    public $latestUsers;
    public $latestPayments;
    public $chartPeriod = 'month';
    public $chartLabels = [];
    public $chartData = [];
    public $categoryNames = [];
    public $categoryData = [];
    public $currentYear;

    public function mount()
    {
        $this->currentYear = Carbon::now()->year;
        $this->loadStats();
        $this->loadChartData();
        $this->loadCategoryData();
    }

    public function loadStats()
    {
        $this->totalAds = Ad::count();
        $this->totalUsers = User::count();
        $this->totalCategories = Category::count();
        $this->totalRevenue = Payment::where('status', 'completed')->sum('amount');

        $this->latestAds = Ad::with(['category', 'user'])
            ->latest()
            ->take(5)
            ->get();

        $this->latestUsers = User::latest()
            ->take(5)
            ->get();

        $this->latestPayments = Payment::with('user')
            ->latest()
            ->take(5)
            ->get();
    }

    public function loadChartData()
    {
        $now = Carbon::now();

        if ($this->chartPeriod === 'month') {
            // Monthly data for the current year
            $data = Ad::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as count')
            )
            ->whereYear('created_at', $now->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

            $months = [
                1 => 'فروردین', 2 => 'اردیبهشت', 3 => 'خرداد',
                4 => 'تیر', 5 => 'مرداد', 6 => 'شهریور',
                7 => 'مهر', 8 => 'آبان', 9 => 'آذر',
                10 => 'دی', 11 => 'بهمن', 12 => 'اسفند'
            ];

            $this->chartLabels = array_values($months);
            $this->chartData = array_map(function($month) use ($data) {
                return $data[$month] ?? 0;
            }, array_keys($months));
        } elseif ($this->chartPeriod === 'week') {
            // Weekly data for the last 12 weeks
            $startDate = $now->copy()->subWeeks(11)->startOfWeek();
            $endDate = $now->copy()->endOfWeek();

            $data = Ad::select(
                DB::raw('YEARWEEK(created_at, 1) as week'),
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('week')
            ->orderBy('week')
            ->get()
            ->pluck('count', 'week')
            ->toArray();

            $weeks = [];
            $weekLabels = [];
            $current = $startDate->copy();

            while ($current <= $endDate) {
                $weekNumber = $current->format('YW');
                $weekLabel = $current->format('d') . ' ' . $this->getPersianMonth($current->month);
                $weeks[$weekNumber] = $weekNumber;
                $weekLabels[] = $weekLabel;
                $current->addWeek();
            }

            $this->chartLabels = $weekLabels;
            $this->chartData = array_map(function($week) use ($data) {
                return $data[$week] ?? 0;
            }, $weeks);
        } else {
            // Daily data for the last 30 days
            $startDate = $now->copy()->subDays(29)->startOfDay();
            $endDate = $now->copy()->endOfDay();

            $data = Ad::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

            $days = [];
            $dayLabels = [];
            $current = $startDate->copy();

            while ($current <= $endDate) {
                $dateString = $current->format('Y-m-d');
                $dayLabel = $current->format('d') . ' ' . $this->getPersianMonth($current->month);
                $days[$dateString] = $dateString;
                $dayLabels[] = $dayLabel;
                $current->addDay();
            }

            $this->chartLabels = $dayLabels;
            $this->chartData = array_map(function($day) use ($data) {
                return $data[$day] ?? 0;
            }, $days);
        }
    }

    public function loadCategoryData()
    {
        $categories = Category::all()
            ->map(function ($category) {
                $category->ads_count = $category->getAllAdsCount();
                return $category;
            })
            ->sortByDesc('ads_count')
            ->take(10);

        $this->categoryNames = $categories->pluck('name')->toArray();
        $this->categoryData = $categories->pluck('ads_count')->toArray();
    }

    public function setChartPeriod($period)
    {
        $this->chartPeriod = $period;
        $this->loadChartData();
        $this->dispatch('chartDataUpdated', [
            'labels' => $this->chartLabels,
            'values' => $this->chartData
        ]);
    }

    private function getPersianMonth($month)
    {
        $months = [
            1 => 'فروردین', 2 => 'اردیبهشت', 3 => 'خرداد',
            4 => 'تیر', 5 => 'مرداد', 6 => 'شهریور',
            7 => 'مهر', 8 => 'آبان', 9 => 'آذر',
            10 => 'دی', 11 => 'بهمن', 12 => 'اسفند'
        ];

        return $months[$month] ?? '';
    }

    public function render()
    {
        return view('livewire.admin.dashboard')
            ->layout('layouts.admin');
    }
}
