<?php

namespace App\Livewire\Admin\Packages;

use App\Models\Package;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $packageToDelete = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 10],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function confirmDelete($packageId)
    {
        $this->packageToDelete = $packageId;
        $this->dispatch('showDeleteModal');
    }

    public function deletePackage()
    {
        if ($this->packageToDelete) {
            $package = Package::find($this->packageToDelete);
            if ($package) {
                $package->delete();
                session()->flash('success', 'پکیج با موفقیت حذف شد.');
            }
            $this->packageToDelete = null;
            $this->dispatch('hideDeleteModal');
        }
    }

    public function togglePackageStatus($packageId)
    {
        $package = Package::find($packageId);
        if ($package) {
            $package->update(['is_active' => !$package->is_active]);
            session()->flash('success', 'وضعیت پکیج با موفقیت تغییر کرد.');
        }
    }

    public function render()
    {
        $query = Package::withCount('ads');

        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->statusFilter !== '') {
            $query->where('is_active', $this->statusFilter);
        }

        $packages = $query->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.admin.packages.index', [
            'packages' => $packages,
        ])->layout('layouts.admin');
    }
}
