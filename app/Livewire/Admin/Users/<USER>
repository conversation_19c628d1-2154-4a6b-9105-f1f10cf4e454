<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class Create extends Component
{
    public $name;
    public $email;
    public $mobile;
    public $password;
    public $password_confirmation;
    public $role = 'user';
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'nullable|email|unique:users,email',
        'mobile' => 'required|string|unique:users,mobile|regex:/^09[0-9]{9}$/',
        'password' => 'required|string|min:8|confirmed',
        'role' => 'required|in:user,admin',
        'is_active' => 'boolean',
    ];

    protected $messages = [
        'mobile.regex' => 'شماره موبایل باید با 09 شروع شده و 11 رقم باشد.',
        'mobile.unique' => 'این شماره موبایل قبلاً ثبت شده است.',
        'email.unique' => 'این ایمیل قبلاً ثبت شده است.',
        'password.confirmed' => 'تکرار رمز عبور مطابقت ندارد.',
        'password.min' => 'رمز عبور باید حداقل 8 کاراکتر باشد.',
    ];

    public function save()
    {
        $this->validate();

        User::create([
            'name' => $this->name,
            'email' => $this->email ?: null,
            'mobile' => $this->mobile,
            'password' => Hash::make($this->password),
            'role' => $this->role,
            'is_active' => $this->is_active,
            'mobile_verified_at' => now(), // Auto verify for admin created users
        ]);

        session()->flash('success', 'کاربر با موفقیت ایجاد شد.');
        return redirect()->route('admin.users.index');
    }

    public function render()
    {
        return view('livewire.admin.users.create')->layout('layouts.admin');
    }
}
