<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $roleFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $userToDelete = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 10],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingRoleFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function confirmDelete($userId)
    {
        $this->userToDelete = $userId;
        $this->dispatch('showDeleteModal');
    }

    public function deleteUser()
    {
        if ($this->userToDelete) {
            $user = User::find($this->userToDelete);
            if ($user) {
                $user->delete();
                session()->flash('success', 'کاربر با موفقیت حذف شد.');
            }
            $this->userToDelete = null;
            $this->dispatch('hideDeleteModal');
        }
    }

    public function toggleUserStatus($userId)
    {
        $user = User::find($userId);
        if ($user) {
            $user->update(['is_active' => !$user->is_active]);
            session()->flash('success', 'وضعیت کاربر با موفقیت تغییر کرد.');
        }
    }

    public function render()
    {
        $query = User::withCount('ads');

        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%')
                  ->orWhere('mobile', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->roleFilter) {
            $query->where('role', $this->roleFilter);
        }

        if ($this->statusFilter !== '') {
            $query->where('is_active', $this->statusFilter);
        }

        $users = $query->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.admin.users.index', [
            'users' => $users,
        ])->layout('layouts.admin');
    }
}
