<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class Edit extends Component
{
    public User $user;
    public $name;
    public $email;
    public $mobile;
    public $password;
    public $password_confirmation;
    public $role;
    public $is_active;

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:users,email,' . $this->user->id,
            'mobile' => 'required|string|unique:users,mobile,' . $this->user->id . '|regex:/^09[0-9]{9}$/',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:user,admin',
            'is_active' => 'boolean',
        ];
    }

    protected $messages = [
        'mobile.regex' => 'شماره موبایل باید با 09 شروع شده و 11 رقم باشد.',
        'mobile.unique' => 'این شماره موبایل قبلاً ثبت شده است.',
        'email.unique' => 'این ایمیل قبلاً ثبت شده است.',
        'password.confirmed' => 'تکرار رمز عبور مطابقت ندارد.',
        'password.min' => 'رمز عبور باید حداقل 8 کاراکتر باشد.',
    ];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->user->load(['ads']);
        
        $this->name = $user->name;
        $this->email = $user->email;
        $this->mobile = $user->mobile;
        $this->role = $user->role;
        $this->is_active = $user->is_active;
    }

    public function confirmDelete()
    {
        $this->dispatch('showDeleteModal');
    }

    public function deleteUser()
    {
        $this->user->delete();
        session()->flash('success', 'کاربر با موفقیت حذف شد.');
        return redirect()->route('admin.users.index');
    }

    public function save()
    {
        $this->validate();

        $updateData = [
            'name' => $this->name,
            'email' => $this->email ?: null,
            'mobile' => $this->mobile,
            'role' => $this->role,
            'is_active' => $this->is_active,
        ];

        // Only update password if provided
        if ($this->password) {
            $updateData['password'] = Hash::make($this->password);
        }

        $this->user->update($updateData);

        session()->flash('success', 'اطلاعات کاربر با موفقیت بروزرسانی شد.');
        return redirect()->route('admin.users.index');
    }

    public function render()
    {
        return view('livewire.admin.users.edit')->layout('layouts.admin');
    }
}
