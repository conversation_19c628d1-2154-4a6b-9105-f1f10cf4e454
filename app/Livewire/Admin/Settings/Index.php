<?php

namespace App\Livewire\Admin\Settings;

use Livewire\Component;
use Livewire\WithFileUploads;

class Index extends Component
{
    use WithFileUploads;

    public $site_name;
    public $site_description;
    public $site_keywords;
    public $contact_email;
    public $contact_phone;
    public $contact_address;
    public $zarinpal_merchant_id;
    public $zarinpal_sandbox;
    public $ads_per_page;
    public $max_images_per_ad;
    public $max_image_size;
    public $free_ad_duration;
    public $featured_ad_price;
    public $premium_ad_price;
    public $ladder_ad_price;
    public $site_logo;
    public $site_favicon;

    protected $rules = [
        'site_name' => 'required|string|max:255',
        'site_description' => 'nullable|string',
        'site_keywords' => 'nullable|string',
        'contact_email' => 'nullable|email',
        'contact_phone' => 'nullable|string|max:20',
        'contact_address' => 'nullable|string',
        'zarinpal_merchant_id' => 'nullable|string|max:36',
        'zarinpal_sandbox' => 'boolean',
        'ads_per_page' => 'required|integer|min:1|max:100',
        'max_images_per_ad' => 'required|integer|min:1|max:10',
        'max_image_size' => 'required|integer|min:1|max:10240', // KB
        'free_ad_duration' => 'required|integer|min:1|max:365',
        'featured_ad_price' => 'required|numeric|min:0',
        'premium_ad_price' => 'required|numeric|min:0',
        'ladder_ad_price' => 'required|numeric|min:0',
        'site_logo' => 'nullable|image|max:2048',
        'site_favicon' => 'nullable|image|max:512',
    ];

    public function mount()
    {
        $this->loadSettings();
    }

    public function loadSettings()
    {
        $this->site_name = config('app.name', 'سایت آگهی');
        $this->site_description = 'سایت آگهی رایگان';
        $this->site_keywords = 'آگهی، رایگان، خرید، فروش';
        $this->contact_email = '<EMAIL>';
        $this->contact_phone = '021-12345678';
        $this->contact_address = 'تهران، ایران';
        $this->zarinpal_merchant_id = '';
        $this->zarinpal_sandbox = true;
        $this->ads_per_page = 12;
        $this->max_images_per_ad = 5;
        $this->max_image_size = 2048; // 2MB
        $this->free_ad_duration = 30; // days
        $this->featured_ad_price = 50000;
        $this->premium_ad_price = 100000;
        $this->ladder_ad_price = 25000;
    }

    public function save()
    {
        $this->validate();

        // Here you would typically save to a settings table or config files
        // For now, we'll just show a success message
        
        session()->flash('success', 'تنظیمات با موفقیت ذخیره شد.');
    }

    public function render()
    {
        return view('livewire.admin.settings.index')->layout('layouts.admin');
    }
}
