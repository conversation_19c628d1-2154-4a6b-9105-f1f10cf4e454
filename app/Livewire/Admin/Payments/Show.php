<?php

namespace App\Livewire\Admin\Payments;

use App\Models\Payment;
use Livewire\Component;

class Show extends Component
{
    public Payment $payment;

    public function mount(Payment $payment)
    {
        $this->payment = $payment;
        $this->payment->load(['user', 'ad', 'ad.category']);
    }

    public function updateStatus($status)
    {
        $this->payment->update(['status' => $status]);
        session()->flash('success', 'وضعیت پرداخت با موفقیت بروزرسانی شد.');
    }

    public function render()
    {
        return view('livewire.admin.payments.show')->layout('layouts.admin');
    }
}
