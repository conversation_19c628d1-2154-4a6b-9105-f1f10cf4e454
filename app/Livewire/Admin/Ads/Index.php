<?php

namespace App\Livewire\Admin\Ads;

use App\Models\Ad;
use App\Models\Category;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $categoryFilter = '';
    public $typeFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $adToDelete = null;
    public $categories = [];

    protected $queryString = [
        'search' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'typeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 10],
    ];

    public function mount()
    {
        $this->categories = Category::orderBy('name')->get();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatingTypeFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function confirmDelete($adId)
    {
        $this->adToDelete = Ad::find($adId);
        $this->dispatch('showDeleteModal');
    }

    public function deleteAd()
    {
        if ($this->adToDelete) {
            $this->adToDelete->delete();
            
            session()->flash('success', 'آگهی با موفقیت حذف شد.');
            $this->dispatch('hideDeleteModal');
            $this->adToDelete = null;
        }
    }

    public function render()
    {
        $query = Ad::with(['category', 'user']);
        
        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('location', 'like', '%' . $this->search . '%');
            });
        }
        
        if ($this->categoryFilter) {
            $query->where('category_id', $this->categoryFilter);
        }
        
        if ($this->typeFilter) {
            if ($this->typeFilter === 'premium') {
                $query->where('is_premium', true);
            } elseif ($this->typeFilter === 'featured') {
                $query->where('is_featured', true)
                      ->where('is_premium', false);
            } elseif ($this->typeFilter === 'ladder') {
                $query->where('type', 'ladder');
            } elseif ($this->typeFilter === 'basic') {
                $query->where('is_premium', false)
                      ->where('is_featured', false)
                      ->where('type', 'basic');
            }
        }
        
        if ($this->statusFilter === 'active') {
            $query->where(function($q) {
                $q->where('expires_at', '>', Carbon::now())
                  ->orWhereNull('expires_at');
            });
        } elseif ($this->statusFilter === 'expired') {
            $query->where('expires_at', '<', Carbon::now())
                  ->whereNotNull('expires_at');
        }
        
        $ads = $query->orderBy($this->sortField, $this->sortDirection)
                    ->paginate($this->perPage);
        
        return view('livewire.admin.ads.index', [
            'ads' => $ads,
        ])->layout('layouts.admin');
    }
}
