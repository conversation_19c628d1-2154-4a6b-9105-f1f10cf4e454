<?php

namespace App\Livewire\Admin\Ads;

use App\Models\Ad;
use App\Models\Category;
use App\Models\Package;
use Carbon\Carbon;
use Livewire\Component;

class Edit extends Component
{
    public Ad $ad;
    public $title;
    public $description;
    public $price;
    public $location;
    public $category_id;
    public $package_id;
    public $type;
    public $is_featured;
    public $is_premium;
    public $expires_at;
    public $categories = [];
    public $packages = [];

    protected function rules()
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'package_id' => 'nullable|exists:packages,id',
            'type' => 'required|in:basic,featured,premium,ladder',
            'is_featured' => 'boolean',
            'is_premium' => 'boolean',
            'expires_at' => 'nullable|date',
        ];
    }

    public function mount(Ad $ad)
    {
        $this->ad = $ad;
        $this->ad->load(['payments']);
        
        $this->title = $ad->title;
        $this->description = $ad->description;
        $this->price = $ad->price;
        $this->location = $ad->location;
        $this->category_id = $ad->category_id;
        $this->package_id = $ad->package_id;
        $this->type = $ad->type;
        $this->is_featured = $ad->is_featured;
        $this->is_premium = $ad->is_premium;
        $this->expires_at = $ad->expires_at ? $ad->expires_at->format('Y-m-d') : null;
        
        $this->categories = Category::orderBy('name')->get();
        $this->packages = Package::where('is_active', true)->orderBy('price')->get();
    }

    public function confirmDelete()
    {
        $this->dispatch('showDeleteModal');
    }

    public function deleteAd()
    {
        $this->ad->delete();
        
        session()->flash('success', 'آگهی با موفقیت حذف شد.');
        $this->dispatch('hideDeleteModal');
        
        return redirect()->route('admin.ads.index');
    }

    public function save()
    {
        $this->validate();

        // Update ad type based on is_premium and is_featured
        if ($this->is_premium) {
            $this->type = 'premium';
        } elseif ($this->is_featured) {
            $this->type = 'featured';
        } elseif ($this->type == 'ladder') {
            $this->type = 'ladder';
        } else {
            $this->type = 'basic';
        }

        $this->ad->update([
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'location' => $this->location,
            'category_id' => $this->category_id,
            'package_id' => $this->package_id,
            'type' => $this->type,
            'is_featured' => $this->is_featured,
            'is_premium' => $this->is_premium,
            'expires_at' => $this->expires_at ? Carbon::parse($this->expires_at) : null,
        ]);

        session()->flash('success', 'آگهی با موفقیت بروزرسانی شد.');
        return redirect()->route('admin.ads.index');
    }

    public function render()
    {
        return view('livewire.admin.ads.edit')
            ->layout('layouts.admin');
    }
}
