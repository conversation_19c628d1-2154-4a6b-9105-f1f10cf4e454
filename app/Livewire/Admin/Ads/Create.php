<?php

namespace App\Livewire\Admin\Ads;

use App\Models\Ad;
use App\Models\Category;
use App\Models\Package;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use WithFileUploads;

    public $title;
    public $description;
    public $price;
    public $location;
    public $category_id;
    public $package_id;
    public $type = 'basic';
    public $is_featured = false;
    public $is_premium = false;
    public $expires_at;
    public $images = [];
    public $primaryImage = 0;
    public $categories = [];
    public $packages = [];

    protected function rules()
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'location' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'package_id' => 'nullable|exists:packages,id',
            'type' => 'required|in:basic,featured,premium,ladder',
            'is_featured' => 'boolean',
            'is_premium' => 'boolean',
            'expires_at' => 'nullable|date',
            'images' => 'required|array|min:1|max:5',
            'images.*' => 'image|max:2048', // 2MB Max
            'primaryImage' => 'required|numeric',
        ];
    }

    public function mount()
    {
        $this->categories = Category::orderBy('name')->get();
        $this->packages = Package::where('is_active', true)->orderBy('price')->get();
        $this->expires_at = Carbon::now()->addDays(30)->format('Y-m-d');
    }

    public function removeImage($index)
    {
        // Remove image at the specified index
        if (isset($this->images[$index])) {
            unset($this->images[$index]);
            $this->images = array_values($this->images); // Re-index array

            // If the primary image was removed, set the first image as primary
            if ($this->primaryImage == $index && count($this->images) > 0) {
                $this->primaryImage = 0;
            } elseif ($this->primaryImage > $index) {
                // Adjust primary image index if it was after the removed image
                $this->primaryImage--;
            }
        }
    }

    public function save()
    {
        $this->validate();

        // Update ad type based on is_premium and is_featured
        if ($this->is_premium) {
            $this->type = 'premium';
        } elseif ($this->is_featured) {
            $this->type = 'featured';
        } elseif ($this->type == 'ladder') {
            $this->type = 'ladder';
        } else {
            $this->type = 'basic';
        }

        // Create the ad
        $ad = Ad::create([
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'location' => $this->location,
            'category_id' => $this->category_id,
            'user_id' => Auth::id(),
            'package_id' => $this->package_id,
            'type' => $this->type,
            'is_featured' => $this->is_featured,
            'is_premium' => $this->is_premium,
            'expires_at' => $this->expires_at ? Carbon::parse($this->expires_at) : null,
        ]);

        // Save images
        foreach ($this->images as $index => $image) {
            $imagePath = $image->store('ads', 'public');

            $ad->images()->create([
                'image_path' => $imagePath,
                'is_primary' => ($index == $this->primaryImage),
                'order' => $index,
            ]);
        }

        session()->flash('success', 'آگهی با موفقیت ایجاد شد.');
        return redirect()->route('admin.ads.index');
    }

    public function render()
    {
        return view('livewire.admin.ads.create');
    }
}
