<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Category;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Livewire\Component;

class EditAd extends Component
{
    use AuthorizesRequests;

    public Ad $ad;
    public $title;
    public $description;
    public $price;
    public $location;
    public $category_id;

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string',
        'price' => 'required|numeric|min:0',
        'location' => 'required|string|max:255',
        'category_id' => 'required|exists:categories,id',
    ];

    public function mount(Ad $ad)
    {
        $this->authorize('update', $ad);
        
        $this->ad = $ad;
        $this->title = $ad->title;
        $this->description = $ad->description;
        $this->price = $ad->price;
        $this->location = $ad->location;
        $this->category_id = $ad->category_id;
    }

    public function update()
    {
        $this->authorize('update', $this->ad);
        
        $this->validate();

        $this->ad->update([
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'location' => $this->location,
            'category_id' => $this->category_id,
        ]);

        session()->flash('success', 'آگهی با موفقیت بروزرسانی شد.');
        return redirect()->route('ads.show', $this->ad);
    }

    public function render()
    {
        return view('livewire.edit-ad', [
            'categories' => Category::all(),
        ]);
    }
}
