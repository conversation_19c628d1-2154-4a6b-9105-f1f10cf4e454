<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;

class Register extends Component
{
    public $name;
    public $mobile = "09902520250";
    public $email;
    public $password = "123456789";
    public $password_confirmation = "123456789";

    public function render()
    {
        return view('livewire.register');

    }

    public function register()
    {

        $this->validate([
            'mobile' => 'required|string|max:255',
            'password' => 'required|string|min:8|confirmed',
        ], [
            'mobile.required' => 'لطفا شماره همراه خود را وارد کنید.',
            'mobile.max'
         => 'شماره همراه نباید بیشتر از 255 کاراکتر باشد.',
            'password.required' => 'لطفا رمز عبور خود را وارد کنید.',
            'password.min' => 'رمز عبور حداقل هشت کاراکتر باشد',
            'password.confirmed' => 'رمزهای عبور وارد شده با هم مطابقت ندارند.',
        ]);


        // $validatedData = $this->validate();

        // $this->validate([
        //     'mobile' => 'required|string|max:255',
        // ], [
        //     'mobile.required' => 'لطفا شماره همراه خود را وارد کنید.',
        //     'mobile.max'
        //  => 'شماره همراه نباید بیشتر از 255 کاراکتر باشد.',
        // ]);

        // $this->validate([
        //     'pass' => 'required|string|min:8|confirmed',
        // ], [
        //     'pass.required' => 'لطفا رمز عبور خود را وارد کنید.',
        //     'pass.min' => 'رمز عبور حداقل هشت کاراکتر باشد',
        //     'pass.confirmed' => 'رمزهای عبور وارد شده با هم مطابقت ندارند.',
        // ]);

        // $this->validate([
        //     'name' => 'required|string|max:255',
        //     'email' => 'required|string|email|max:255|unique:users',
        //     'password' => 'required|string|min:8|confirmed',
        // ], [
        //     'name.required' => 'لطفا نام خود را وارد کنید.',
        //     'name.max'
        //  => 'نام نباید بیشتر از 255 کاراکتر باشد.',
        //     'email.required' => 'لطفا ایمیل خود را وارد کنید.',
        //     'email.max' => 'ایمیل نباید بیشتر از 255 کاراکتر باشد.',
        //     'password.required' => 'لطفا رمز عبور خود را وارد کنید.',
        //     'password.min' => 'رمز عبور حداقل هشت کاراکتر باشد',
        //     'password.confirmed' => 'رمزهای عبور وارد شده با هم مطابقت ندارند.',
        // ]);

        User::create([
            'mobile' => $this->mobile,
            'password'
 => bcrypt($this->password),
        ]);

        session()->flash('message', 'ثبت نام با موفقیت انجام شد.');
        return redirect()->route('login');
    }
}
