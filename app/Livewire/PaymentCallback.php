<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Zarinpal\Zarinpal;

class PaymentCallback extends Component
{
    public $success = false;
    public $error = '';
    public $payment;
    public $ad;

    public function mount()
    {
        $authority = request()->get('Authority');
        $status = request()->get('Status');

        if ($status !== 'OK' || empty($authority)) {
            $this->error = 'پرداخت توسط کاربر لغو شد.';
            return;
        }

        // Find the payment by transaction ID
        $this->payment = Payment::where('transaction_id', $authority)->first();

        if (!$this->payment) {
            $this->error = 'اطلاعات پرداخت یافت نشد.';
            return;
        }

        $this->ad = Ad::find($this->payment->ad_id);

        if (!$this->ad) {
            $this->error = 'آگهی مورد نظر یافت نشد.';
            return;
        }

        // Initialize Zarinpal
        $zarinpal = new Zarinpal(config('services.zarinpal.merchant_id'));
        $zarinpal->enableSandbox(); // Remove this line in production

        // Verify payment
        $results = $zarinpal->verify($this->payment->amount, $authority);

        if ($results['Status'] == 100) {
            // Payment successful
            $this->success = true;
            
            // Update payment status
            $this->payment->update([
                'status' => 'completed',
                'reference_id' => $results['RefID'],
            ]);

            // Update ad with expiration date
            if ($this->ad->package) {
                $this->ad->update([
                    'expires_at' => Carbon::now()->addDays($this->ad->package->duration_days),
                ]);
            }

            session()->flash('success', 'پرداخت با موفقیت انجام شد.');
        } else {
            // Payment failed
            $this->error = 'خطا در تایید پرداخت: ' . $results['Status'];
            
            // Update payment status
            $this->payment->update([
                'status' => 'failed',
            ]);

            session()->flash('error', $this->error);
        }
    }

    public function render()
    {
        return view('livewire.payment-callback');
    }
}
