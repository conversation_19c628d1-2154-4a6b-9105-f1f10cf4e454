<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdController extends Controller
{
    /**
     * Display a listing of the ads.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $ads = Auth::check() && Auth::user()->hasRole('admin')
            ? Ad::with('category', 'user')->latest()->paginate(10)
            : Ad::with('category', 'user')->where('user_id', Auth::id())->latest()->paginate(10);
            
        return view('ads.index', compact('ads'));
    }

    /**
     * Show the form for creating a new ad.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::all();
        return view('ads.create', compact('categories'));
    }

    /**
     * Store a newly created ad in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'location' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
        ]);

        $ad = new Ad($validated);
        $ad->user_id = Auth::id();
        $ad->save();

        return redirect()->route('ads.show', $ad)->with('success', 'آگهی با موفقیت ایجاد شد.');
    }

    /**
     * Display the specified ad.
     *
     * @param  \App\Models\Ad  $ad
     * @return \Illuminate\Http\Response
     */
    public function show(Ad $ad)
    {
        return view('ads.show', compact('ad'));
    }

    /**
     * Show the form for editing the specified ad.
     *
     * @param  \App\Models\Ad  $ad
     * @return \Illuminate\Http\Response
     */
    public function edit(Ad $ad)
    {
        $this->authorize('update', $ad);
        
        $categories = Category::all();
        return view('ads.edit', compact('ad', 'categories'));
    }

    /**
     * Update the specified ad in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Ad  $ad
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Ad $ad)
    {
        $this->authorize('update', $ad);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'location' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
        ]);

        $ad->update($validated);

        return redirect()->route('ads.show', $ad)->with('success', 'آگهی با موفقیت بروزرسانی شد.');
    }

    /**
     * Remove the specified ad from storage.
     *
     * @param  \App\Models\Ad  $ad
     * @return \Illuminate\Http\Response
     */
    public function destroy(Ad $ad)
    {
        $this->authorize('delete', $ad);
        
        $ad->delete();

        return redirect()->route('ads.index')->with('success', 'آگهی با موفقیت حذف شد.');
    }
}
