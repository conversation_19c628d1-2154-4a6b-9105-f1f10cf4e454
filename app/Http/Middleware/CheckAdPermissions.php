<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckAdPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // اگر کاربر نقش مدیر را داشته باشد، به درخواست اجازه عبور داده می‌شود
        if (auth()->user()->hasRole('admin')) {
            return $next($request);
        }

        // در غیر این صورت، بررسی می‌شود که آیا کاربر در حال ویرایش یا حذف آگهی خود است
        if ($request->route()->named('ads.edit') || $request->route()->named('ads.destroy')) {
            $ad = Ad::findOrFail($request->route('ad'));
            if ($ad->user_id !== auth()->id()) {
                abort(403, 'شما مجوز دسترسی به این آگهی را ندارید.');
            }
        }

        return $next($request);
    }
}
