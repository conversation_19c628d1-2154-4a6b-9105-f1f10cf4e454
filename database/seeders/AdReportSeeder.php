<?php

namespace Database\Seeders;

use App\Models\AdReport;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AdReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::all();
        $users = User::all();
        
        // دلایل مختلف گزارش
        $reasons = [
            'fake' => 'آگهی جعلی',
            'offensive' => 'محتوای نامناسب',
            'sold' => 'کالا فروخته شده',
            'other' => 'سایر موارد'
        ];
        
        // توضیحات نمونه برای هر دلیل
        $descriptions = [
            'fake' => [
                'این آگهی جعلی است و کالای واقعی وجود ندارد.',
                'تصاویر از سایت‌های دیگر کپی شده است.',
                'شماره تماس پاسخ نمی‌دهد و آگهی جعلی است.',
                'قیمت غیرواقعی و مشکوک است.',
                'آگهی‌دهنده هویت واقعی ندارد.'
            ],
            'offensive' => [
                'محتوای آگهی نامناسب و توهین‌آمیز است.',
                'تصاویر نامناسب و غیراخلاقی دارد.',
                'متن آگهی حاوی کلمات رکیک است.',
                'محتوای مغایر با قوانین سایت است.',
                'آگهی حاوی تبلیغات نامناسب است.'
            ],
            'sold' => [
                'کالا فروخته شده اما آگهی هنوز فعال است.',
                'آگهی‌دهنده اعلام کرده کالا موجود نیست.',
                'کالا از مدت‌ها پیش فروخته شده است.',
                'آگهی منقضی شده و باید حذف شود.',
                'کالا دیگر در دسترس نیست.'
            ],
            'other' => [
                'آگهی در دسته‌بندی اشتباه قرار گرفته است.',
                'اطلاعات تماس نادرست است.',
                'آگهی تکراری و قبلاً ثبت شده است.',
                'قیمت با توضیحات مطابقت ندارد.',
                'آگهی مربوط به خدمات غیرقانونی است.',
                'محل آگهی با واقعیت مطابقت ندارد.',
                'آگهی‌دهنده پاسخگو نیست.',
                'کیفیت کالا با توضیحات متفاوت است.'
            ]
        ];
        
        // ایجاد 30-50 گزارش تصادفی
        $reportCount = rand(30, 50);
        
        for ($i = 0; $i < $reportCount; $i++) {
            $ad = $ads->random();
            $user = $users->random();
            $reason = array_rand($reasons);
            
            // اطمینان از اینکه کاربر قبلاً همین آگهی را گزارش نکرده
            $existingReport = AdReport::where('ad_id', $ad->id)
                                   ->where('user_id', $user->id)
                                   ->first();
            
            if ($existingReport) {
                continue; // اگر قبلاً گزارش داده، به آگهی بعدی برو
            }
            
            $description = '';
            if ($reason === 'other') {
                $description = $descriptions[$reason][array_rand($descriptions[$reason])];
            } else {
                // 70% احتمال اینکه توضیحات اضافی بدهد
                if (rand(1, 100) <= 70) {
                    $description = $descriptions[$reason][array_rand($descriptions[$reason])];
                }
            }
            
            // تعیین وضعیت حل شدن (30% احتمال حل شده باشد)
            $isResolved = rand(1, 100) <= 30;
            $resolvedAt = null;
            
            if ($isResolved) {
                $createdAt = fake()->dateTimeBetween('-60 days', '-1 day');
                $resolvedAt = fake()->dateTimeBetween($createdAt, 'now');
            } else {
                $createdAt = fake()->dateTimeBetween('-30 days', 'now');
            }
            
            AdReport::create([
                'ad_id' => $ad->id,
                'user_id' => $user->id,
                'reason' => $reason,
                'description' => $description,
                'is_resolved' => $isResolved,
                'resolved_at' => $resolvedAt,
                'created_at' => $createdAt,
                'updated_at' => $resolvedAt ?: $createdAt,
            ]);
        }
    }
}
