<?php

namespace Database\Seeders;

use App\Models\Package;
use Illuminate\Database\Seeder;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Free package
        Package::create([
            'name' => 'رایگان',
            'description' => 'آگهی رایگان با امکانات پایه',
            'price' => 0,
            'duration_days' => 30,
            'is_featured' => false,
            'is_premium' => false,
            'is_active' => true,
        ]);

        // Featured package
        Package::create([
            'name' => 'ویژه',
            'description' => 'آگهی ویژه با نمایش در بخش ویژه‌ها',
            'price' => 50000,
            'duration_days' => 30,
            'is_featured' => true,
            'is_premium' => false,
            'is_active' => true,
        ]);

        // Premium package
        Package::create([
            'name' => 'طلایی',
            'description' => 'آگهی طلایی با نمایش در بالای لیست و صفحه اصلی',
            'price' => 100000,
            'duration_days' => 30,
            'is_featured' => true,
            'is_premium' => true,
            'is_active' => true,
        ]);

        // Ladder package
        Package::create([
            'name' => 'نردبانی',
            'description' => 'آگهی نردبانی با نمایش دوره‌ای در بالای لیست',
            'price' => 75000,
            'duration_days' => 30,
            'is_featured' => false,
            'is_premium' => false,
            'is_active' => true,
        ]);
    }
}
