<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin user
        User::firstOrCreate([
            'name' => '<PERSON>',
            'role_id' => '1',
            'email' => '<EMAIL>',
            'mobile' => '09902520250',
            'avatar' => 'users/default.png',
            'password' => bcrypt('mnba110'),
        ]);

        // Regular users
        $users = [
            ['name' => 'زهرا حسینی', 'mobile' => '09177777777', 'email' => '<EMAIL>'],
            ['name' => 'علی احمدی', 'mobile' => '09122222222', 'email' => '<EMAIL>'],
            ['name' => 'فاطمه رضایی', 'mobile' => '09133333333', 'email' => '<EMAIL>'],
            ['name' => 'محمد کریمی', 'mobile' => '09144444444', 'email' => '<EMAIL>'],
            ['name' => 'مریم صادقی', 'mobile' => '09155555555', 'email' => '<EMAIL>'],
            ['name' => 'حسین نوری', 'mobile' => '09166666666', 'email' => '<EMAIL>'],
            ['name' => 'سارا محمدی', 'mobile' => '09188888888', 'email' => '<EMAIL>'],
            ['name' => 'رضا علیزاده', 'mobile' => '09199999999', 'email' => '<EMAIL>'],
            ['name' => 'نرگس حسنی', 'mobile' => '09101010101', 'email' => '<EMAIL>'],
            ['name' => 'امیر قاسمی', 'mobile' => '09121212121', 'email' => '<EMAIL>'],
            ['name' => 'لیلا یوسفی', 'mobile' => '09131313131', 'email' => '<EMAIL>'],
            ['name' => 'بهرام مرادی', 'mobile' => '09141414141', 'email' => '<EMAIL>'],
            ['name' => 'شیما باقری', 'mobile' => '09151515151', 'email' => '<EMAIL>'],
            ['name' => 'داود شریفی', 'mobile' => '09161616161', 'email' => '<EMAIL>'],
            ['name' => 'طاهره فرهادی', 'mobile' => '09171717171', 'email' => '<EMAIL>'],
        ];

        foreach ($users as $userData) {
            User::firstOrCreate([
                'name' => $userData['name'],
                'role_id' => '2',
                'mobile' => $userData['mobile'],
                'email' => $userData['email'],
                'password' => bcrypt('password123'),
                'mobile_verified_at' => now(),
            ]);
        }
    }
}
