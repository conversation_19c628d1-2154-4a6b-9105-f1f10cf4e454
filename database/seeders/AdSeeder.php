<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ad;
use App\Models\Category;
use App\Models\User;
use App\Models\Package;
use Carbon\Carbon;

class AdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $users = User::all();
        $packages = Package::all();

        // آرایه‌ای از عناوین آگهی‌های مختلف بر اساس دسته‌بندی
        $adTitlesByCategory = [
            // املاک
            'real-estate' => [
                'فروش آپارتمان 3 خوابه در تهران',
                'اجاره خانه ویلایی در شمال تهران',
                'فروش زمین مسکونی در کرج',
                'اجاره دفتر کار در مرکز شهر',
                'فروش مغازه در خیابان اصلی',
                'اجاره آپارتمان مبله در شیراز',
                'فروش ویلا در شهرک غرب',
                'اجاره انبار در جنوب تهران',
            ],
            'residential-sale' => [
                'فروش آپارتمان 3 خوابه در تهران',
                'فروش خانه ویلایی 200 متری',
                'فروش آپارتمان نوساز در کرج',
                'فروش ویلا دوبلکس در شمال',
                'فروش آپارتمان 2 خوابه مرکز شهر',
            ],
            'residential-rent' => [
                'اجاره آپارتمان مبله در تهران',
                'اجاره خانه ویلایی در شیراز',
                'اجاره آپارتمان 2 خوابه در کرج',
                'اجاره خانه نقلی در مرکز شهر',
                'اجاره آپارتمان لوکس در شمال',
            ],
            'commercial-sale' => [
                'فروش مغازه در خیابان اصلی',
                'فروش دفتر کار 100 متری',
                'فروش مجتمع تجاری',
                'فروش انبار 500 متری',
                'فروش ساختمان اداری',
            ],
            'commercial-rent' => [
                'اجاره مغازه در مرکز شهر',
                'اجاره دفتر کار مجهز',
                'اجاره انبار در جنوب تهران',
                'اجاره فضای تجاری',
                'اجاره دفتر در برج تجاری',
            ],

            // وسایل نقلیه
            'vehicles' => [
                'فروش پژو 206 مدل 1400',
                'فروش سمند EF7 دوگانه',
                'فروش پراید 131 تمیز',
                'فروش دنا پلاس توربو',
                'فروش تیبا 2 هاچبک',
                'فروش ریو هاچبک',
                'فروش پژو 207 اتوماتیک',
                'فروش ساینا دنده‌ای',
            ],
            'cars' => [
                'فروش پژو 206 مدل 1400',
                'فروش سمند EF7 دوگانه',
                'فروش پراید 131 تمیز',
                'فروش دنا پلاس توربو',
                'فروش تیبا 2 هاچبک',
                'فروش ریو هاچبک',
                'فروش پژو 207 اتوماتیک',
                'فروش ساینا دنده‌ای',
            ],
            'motorcycles' => [
                'فروش موتور هوندا 125',
                'فروش موتور یامaha 150',
                'فروش موتور باجاج پالس',
                'فروش موتور کویر 200',
                'فروش موتور سوزوکی 250',
                'فروش موتور هارلی دیویدسون',
                'فروش موتور کاوازاکی نینجا',
                'فروش موتور هوندا CBR',
            ],
            'heavy-vehicles' => [
                'فروش کامیون بنز 1924',
                'فروش کامیونت نیسان',
                'فروش اتوبوس مسافربری',
                'فروش تریلر کشنده',
                'فروش لودر کاترپیلار',
                'فروش بیل مکانیکی',
                'فروش کامیون کمپرسی',
                'فروش ون بار نیسان',
            ],
            'vehicle-parts' => [
                'فروش لاستیک خودرو',
                'فروش باتری ماشین',
                'فروش قطعات پژو 206',
                'فروش موتور خودرو',
                'فروش گیربکس اتوماتیک',
                'فروش سیستم صوتی ماشین',
                'فروش چراغ خودرو',
                'فروش فیلتر هوای خودرو',
            ],

            // لوازم الکترونیکی
            'electronics' => [
                'فروش آیفون 13 پرو مکس',
                'فروش سامسونگ گلکسی S22',
                'فروش لپ‌تاپ ایسوس گیمینگ',
                'فروش تلویزیون 55 اینچ',
                'فروش کامپیوتر دسکتاپ',
                'فروش تبلت آیپد',
                'فروش کنسول PS5',
                'فروش دوربین کانن',
            ],
            'mobile-tablet' => [
                'فروش آیفون 13 پرو مکس',
                'فروش سامسونگ گلکسی S22',
                'فروش شیائومی ردمی نوت 11',
                'فروش هواوی P40 لایت',
                'فروش آیپد ایر نسل 4',
                'فروش تبلت سامسونگ تب A8',
                'فروش گوشی نوکیا کلاسیک',
                'فروش آیفون SE نسل 2',
            ],
            'laptop-computer' => [
                'فروش لپ‌تاپ ایسوس گیمینگ',
                'فروش مک‌بوک پرو 13 اینچ',
                'فروش کامپیوتر دسکتاپ گیمینگ',
                'فروش لپ‌تاپ لنوو تینک‌پد',
                'فروش مانیتور 27 اینچ',
                'فروش کیبورد و ماوس گیمینگ',
                'فروش پرینتر لیزری HP',
                'فروش هارد اکسترنال 2 ترابایت',
            ],
            'gaming' => [
                'فروش کنسول PS5 نو',
                'فروش Xbox Series X',
                'فروش Nintendo Switch',
                'فروش دسته بازی',
                'فروش بازی PS4',
                'فروش کارت گرافیک گیمینگ',
                'فروش صندلی گیمینگ',
                'فروش هدست گیمینگ',
            ],
            'audio-video' => [
                'فروش تلویزیون 55 اینچ سامسونگ',
                'فروش سیستم صوتی خانگی',
                'فروش اسپیکر بلوتوث',
                'فروش هدفون بی‌سیم',
                'فروش پروژکتور خانگی',
                'فروش ساندبار',
                'فروش دوربین فیلمبرداری',
                'فروش میکروفون استودیویی',
            ],

            // خانه و آشپزخانه
            'home-kitchen' => [
                'فروش یخچال فریزر سامسونگ',
                'فروش ماشین لباسشویی ال جی',
                'فروش کولر گازی اسپلیت',
                'فروش مبل راحتی 7 نفره',
                'فروش میز ناهارخوری چوبی',
                'فروش جاروبرقی بوش',
                'فروش اجاق گاز 5 شعله',
                'فروش ظرف شویی بوش',
            ],
            'furniture-decor' => [
                'فروش مبل راحتی 7 نفره',
                'فروش میز ناهارخوری چوبی',
                'فروش تخت خواب دو نفره',
                'فروش کمد دیواری',
                'فروش میز تلویزیون',
                'فروش فرش دستباف',
                'فروش آینه قدی',
                'فروش لوستر کریستالی',
            ],
            'home-appliances' => [
                'فروش یخچال فریزر سامسونگ',
                'فروش ماشین لباسشویی ال جی',
                'فروش کولر گازی اسپلیت',
                'فروش جاروبرقی بوش',
                'فروش اجاق گاز 5 شعله',
                'فروش ظرف شویی بوش',
                'فروش مایکروویو پاناسونیک',
                'فروش آبمیوه گیری فیلیپس',
            ],
            'kitchenware' => [
                'فروش سرویس قابلمه استیل',
                'فروش ظروف پایرکس',
                'فروش چاقو آشپزخانه',
                'فروش تابه تفلون',
                'فروش کتری برقی',
                'فروش بلندر فیلیپس',
                'فروش توستر نان',
                'فروش ترازوی آشپزخانه',
            ],

            // خدمات
            'services' => [
                'ارائه خدمات تعمیرات لوازم خانگی',
                'خدمات نظافت منزل',
                'آموزش زبان انگلیسی',
                'مشاوره حقوقی',
                'خدمات باربری',
                'تعمیر کامپیوتر و لپ‌تاپ',
                'خدمات نقاشی ساختمان',
                'آموزش رانندگی',
            ],
            'repairs' => [
                'تعمیر لوازم خانگی',
                'تعمیر کامپیوتر و لپ‌تاپ',
                'تعمیر موبایل و تبلت',
                'تعمیر خودرو',
                'تعمیر کولر گازی',
                'تعمیر تلویزیون',
                'تعمیر ماشین لباسشویی',
                'تعمیر یخچال فریزر',
            ],
            'home-services' => [
                'خدمات نظافت منزل',
                'خدمات باربری',
                'خدمات نقاشی ساختمان',
                'خدمات لوله کشی',
                'خدمات برق کاری',
                'خدمات نگهداری از کودک',
                'خدمات نگهداری از سالمند',
                'خدمات آشپزی',
            ],
            'education' => [
                'آموزش زبان انگلیسی',
                'آموزش رانندگی',
                'آموزش کامپیوتر',
                'آموزش موسیقی',
                'آموزش نقاشی',
                'آموزش ریاضی',
                'آموزش زبان فرانسه',
                'آموزش برنامه نویسی',
            ],
            'consulting' => [
                'مشاوره حقوقی',
                'مشاوره املاک',
                'مشاوره سرمایه گذاری',
                'مشاوره ازدواج',
                'مشاوره تحصیلی',
                'مشاوره کسب و کار',
                'مشاوره مالی',
                'مشاوره روانشناسی',
            ],

            // استخدام و کاریابی
            'jobs' => [
                'استخدام منشی',
                'استخدام راننده',
                'استخدام فروشنده',
                'استخدام حسابدار',
                'استخدام برنامه نویس',
                'درخواست کار اداری',
                'درخواست کار فنی',
                'درخواست کار پاره وقت',
            ],
            'hiring' => [
                'استخدام منشی',
                'استخدام راننده',
                'استخدام فروشنده',
                'استخدام حسابدار',
                'استخدام برنامه نویس',
                'استخدام مهندس',
                'استخدام پزشک',
                'استخدام معلم',
            ],
            'job-seeking' => [
                'درخواست کار اداری',
                'درخواست کار فنی',
                'درخواست کار پاره وقت',
                'درخواست کار حسابداری',
                'درخواست کار فروش',
                'درخواست کار رانندگی',
                'درخواست کار نظافت',
                'درخواست کار آشپزی',
            ],
        ];

        $descriptions = [
            'در حد نو، تمیز و بدون خرابی',
            'کیفیت عالی، قیمت مناسب',
            'فوری فروش، قیمت توافقی',
            'تست شده، گارانتی معتبر',
            'کم استفاده، نگهداری شده',
            'قیمت ثابت، جای چانه زدن نیست',
            'فروش به دلیل مهاجرت',
            'تحویل فوری، پرداخت نقدی',
            'شرایط پرداخت آسان',
            'قابل تعویض با کالای مشابه',
        ];

        $locations = [
            'تهران', 'کرج', 'اصفهان', 'شیراز', 'مشهد', 'تبریز',
            'اهواز', 'کرمان', 'رشت', 'قم', 'زاهدان', 'همدان',
            'یزد', 'اردبیل', 'بندرعباس', 'کرمانشاه', 'گرگان', 'ساری'
        ];

        // ایجاد 100 آگهی
        for ($i = 0; $i < 100; $i++) {
            $category = $categories->random();
            $user = $users->random();
            $package = $packages->random();

            // تعیین نوع آگهی بر اساس پکیج
            $type = 'basic';
            $isFeatured = false;
            $isPremium = false;

            if ($package->is_premium) {
                $type = 'premium';
                $isPremium = true;
                $isFeatured = true;
            } elseif ($package->is_featured) {
                $type = 'featured';
                $isFeatured = true;
            } elseif ($package->name === 'نردبانی') {
                $type = 'ladder';
            }

            // تاریخ انقضا
            $expiresAt = Carbon::now()->addDays($package->duration_days);

            // انتخاب عنوان مناسب بر اساس دسته‌بندی
            $categoryTitles = $adTitlesByCategory[$category->slug] ?? $adTitlesByCategory['real-estate'];
            $title = $categoryTitles[array_rand($categoryTitles)];

            Ad::create([
                'title' => $title,
                'description' => $descriptions[array_rand($descriptions)] . '. ' . fake()->paragraph(2),
                'price' => fake()->numberBetween(100000, 5000000000),
                'location' => $locations[array_rand($locations)],
                'category_id' => $category->id,
                'user_id' => $user->id,
                'package_id' => $package->price > 0 ? $package->id : null,
                'type' => $type,
                'is_featured' => $isFeatured,
                'is_premium' => $isPremium,
                'expires_at' => $expiresAt,
                'created_at' => fake()->dateTimeBetween('-60 days', 'now'),
                'updated_at' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }
    }
}
