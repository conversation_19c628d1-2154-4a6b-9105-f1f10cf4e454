<?php

namespace Database\Seeders;

use App\Models\Message;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Database\Seeder;

class MessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::all();
        $users = User::all();
        
        // پیام‌های نمونه برای آگهی‌ها
        $sampleMessages = [
            'سلام، آیا این کالا هنوز موجود است؟',
            'قیمت نهایی چقدر است؟',
            'امکان تست کردن کالا وجود دارد؟',
            'آیا امکان تخفیف هست؟',
            'کالا در چه وضعیتی است؟',
            'آیا گارانتی دارد؟',
            'چه زمانی می‌توانم ببینمش؟',
            'آیا امکان ارسال به شهرستان دارید؟',
            'لطفاً شماره تماس خود را بدهید.',
            'آیا امکان معاوضه با کالای مشابه هست؟',
            'کالا چند وقت استفاده شده؟',
            'آیا لوازم جانبی هم دارد؟',
            'دلیل فروش چیست؟',
            'آیا امکان پرداخت اقساطی دارید؟',
            'کالا اصل است یا کپی؟',
            'آیا مدارک و فاکتور دارد؟',
            'چه زمانی خریداری شده؟',
            'آیا تعمیر شده یا خرابی داشته؟',
            'آیا امکان بازدید حضوری هست؟',
            'لطفاً عکس‌های بیشتری بفرستید.',
        ];
        
        // پاسخ‌های نمونه
        $sampleReplies = [
            'بله موجود است.',
            'قیمت همان مبلغ اعلام شده است.',
            'بله، می‌توانید تست کنید.',
            'متأسفانه تخفیف نداریم.',
            'در حد نو و تمیز است.',
            'بله، گارانتی معتبر دارد.',
            'هر روز از ساعت 9 تا 18 امکان بازدید دارید.',
            'بله، ارسال به سراسر کشور داریم.',
            'شماره تماس: 09123456789',
            'فعلاً امکان معاوضه نداریم.',
            'حدود 6 ماه استفاده شده.',
            'بله، تمام لوازم جانبی موجود است.',
            'به دلیل تغییر سلیقه می‌فروشم.',
            'فقط پرداخت نقدی.',
            'کاملاً اصل و اورجینال است.',
            'بله، فاکتور و گارانتی دارد.',
            'سال گذشته خریداری شده.',
            'هیچ تعمیری نشده و سالم است.',
            'بله، لطفاً هماهنگ کنید.',
            'عکس‌های بیشتر در واتساپ می‌فرستم.',
        ];
        
        // ایجاد 80-120 پیام تصادفی
        $messageCount = rand(80, 120);
        
        for ($i = 0; $i < $messageCount; $i++) {
            $ad = $ads->random();
            $sender = $users->random();
            $receiver = $ad->user; // صاحب آگهی
            
            // اطمینان از اینکه فرستنده و گیرنده متفاوت باشند
            if ($sender->id === $receiver->id) {
                continue;
            }
            
            $isRead = rand(1, 100) <= 70; // 70% احتمال خوانده شدن
            $createdAt = fake()->dateTimeBetween('-30 days', 'now');
            $readAt = null;
            
            if ($isRead) {
                $readAt = fake()->dateTimeBetween($createdAt, 'now');
            }
            
            // ایجاد پیام اولیه
            $initialMessage = Message::create([
                'sender_id' => $sender->id,
                'receiver_id' => $receiver->id,
                'ad_id' => $ad->id,
                'message' => $sampleMessages[array_rand($sampleMessages)],
                'is_read' => $isRead,
                'read_at' => $readAt,
                'created_at' => $createdAt,
                'updated_at' => $readAt ?: $createdAt,
            ]);
            
            // 60% احتمال اینکه پاسخ داده شود
            if (rand(1, 100) <= 60) {
                $replyCreatedAt = fake()->dateTimeBetween($createdAt, 'now');
                $replyIsRead = rand(1, 100) <= 80; // 80% احتمال خوانده شدن پاسخ
                $replyReadAt = null;
                
                if ($replyIsRead) {
                    $replyReadAt = fake()->dateTimeBetween($replyCreatedAt, 'now');
                }
                
                Message::create([
                    'sender_id' => $receiver->id,
                    'receiver_id' => $sender->id,
                    'ad_id' => $ad->id,
                    'message' => $sampleReplies[array_rand($sampleReplies)],
                    'is_read' => $replyIsRead,
                    'read_at' => $replyReadAt,
                    'created_at' => $replyCreatedAt,
                    'updated_at' => $replyReadAt ?: $replyCreatedAt,
                ]);
                
                // 30% احتمال ادامه مکالمه
                if (rand(1, 100) <= 30) {
                    $followUpCreatedAt = fake()->dateTimeBetween($replyCreatedAt, 'now');
                    $followUpIsRead = rand(1, 100) <= 50;
                    $followUpReadAt = null;
                    
                    if ($followUpIsRead) {
                        $followUpReadAt = fake()->dateTimeBetween($followUpCreatedAt, 'now');
                    }
                    
                    $followUpMessages = [
                        'ممنون از پاسختان.',
                        'چه زمانی می‌توانم بیایم؟',
                        'آدرس دقیق کجاست؟',
                        'باشه، فردا می‌آیم.',
                        'خیلی ممنون.',
                    ];
                    
                    Message::create([
                        'sender_id' => $sender->id,
                        'receiver_id' => $receiver->id,
                        'ad_id' => $ad->id,
                        'message' => $followUpMessages[array_rand($followUpMessages)],
                        'is_read' => $followUpIsRead,
                        'read_at' => $followUpReadAt,
                        'created_at' => $followUpCreatedAt,
                        'updated_at' => $followUpReadAt ?: $followUpCreatedAt,
                    ]);
                }
            }
        }
    }
}
