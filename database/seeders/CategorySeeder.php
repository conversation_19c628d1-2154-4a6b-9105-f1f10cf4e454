<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Real Estate Category
        $realEstate = Category::create([
            'name' => 'املاک',
            'slug' => 'real-estate',
            'description' => 'آگهی‌های مربوط به فروش، اجاره و رهن املاک مسکونی، تجاری و اداری',
            'icon' => 'fa-home',
            'is_featured' => true,
            'order' => 1,
            'meta_title' => 'آگهی‌های املاک | خرید، فروش و اجاره ملک',
            'meta_description' => 'آگهی‌های خرید، فروش، رهن و اجاره انواع املاک مسکونی، تجاری و اداری',
            'meta_keywords' => 'املاک، خرید خانه، اجاره خانه، رهن، فروش آپارتمان',
        ]);

        // Real Estate Subcategories
        Category::create([
            'name' => 'فروش مسکونی',
            'slug' => 'residential-sale',
            'description' => 'آگهی‌های فروش انواع املاک مسکونی',
            'parent_id' => $realEstate->id,
            'icon' => 'fa-building',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'اجاره مسکونی',
            'slug' => 'residential-rent',
            'description' => 'آگهی‌های اجاره و رهن انواع املاک مسکونی',
            'parent_id' => $realEstate->id,
            'icon' => 'fa-key',
            'order' => 2,
        ]);

        Category::create([
            'name' => 'فروش تجاری و اداری',
            'slug' => 'commercial-sale',
            'description' => 'آگهی‌های فروش انواع املاک تجاری و اداری',
            'parent_id' => $realEstate->id,
            'icon' => 'fa-store',
            'order' => 3,
        ]);

        Category::create([
            'name' => 'اجاره تجاری و اداری',
            'slug' => 'commercial-rent',
            'description' => 'آگهی‌های اجاره انواع املاک تجاری و اداری',
            'parent_id' => $realEstate->id,
            'icon' => 'fa-briefcase',
            'order' => 4,
        ]);

        // Vehicles Category
        $vehicles = Category::create([
            'name' => 'وسایل نقلیه',
            'slug' => 'vehicles',
            'description' => 'آگهی‌های خرید و فروش انواع وسایل نقلیه',
            'icon' => 'fa-car',
            'is_featured' => true,
            'order' => 2,
            'meta_title' => 'آگهی‌های وسایل نقلیه | خرید و فروش خودرو',
            'meta_description' => 'آگهی‌های خرید و فروش انواع خودرو، موتورسیکلت و وسایل نقلیه سنگین',
            'meta_keywords' => 'خودرو، ماشین، موتورسیکلت، خرید خودرو، فروش خودرو',
        ]);

        // Vehicles Subcategories
        Category::create([
            'name' => 'سواری',
            'slug' => 'cars',
            'description' => 'آگهی‌های خرید و فروش انواع خودروهای سواری',
            'parent_id' => $vehicles->id,
            'icon' => 'fa-car-side',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'موتورسیکلت',
            'slug' => 'motorcycles',
            'description' => 'آگهی‌های خرید و فروش انواع موتورسیکلت',
            'parent_id' => $vehicles->id,
            'icon' => 'fa-motorcycle',
            'order' => 2,
        ]);

        Category::create([
            'name' => 'خودروهای سنگین',
            'slug' => 'heavy-vehicles',
            'description' => 'آگهی‌های خرید و فروش انواع خودروهای سنگین',
            'parent_id' => $vehicles->id,
            'icon' => 'fa-truck',
            'order' => 3,
        ]);

        Category::create([
            'name' => 'قطعات یدکی و لوازم جانبی',
            'slug' => 'vehicle-parts',
            'description' => 'آگهی‌های خرید و فروش انواع قطعات یدکی و لوازم جانبی وسایل نقلیه',
            'parent_id' => $vehicles->id,
            'icon' => 'fa-cogs',
            'order' => 4,
        ]);

        // Electronics Category
        $electronics = Category::create([
            'name' => 'لوازم الکترونیکی',
            'slug' => 'electronics',
            'description' => 'آگهی‌های خرید و فروش انواع لوازم الکترونیکی',
            'icon' => 'fa-laptop',
            'is_featured' => true,
            'order' => 3,
            'meta_title' => 'آگهی‌های لوازم الکترونیکی | خرید و فروش',
            'meta_description' => 'آگهی‌های خرید و فروش انواع لوازم الکترونیکی، موبایل، تبلت، لپ تاپ و کامپیوتر',
            'meta_keywords' => 'موبایل، لپ تاپ، تبلت، کامپیوتر، لوازم الکترونیکی',
        ]);

        // Electronics Subcategories
        Category::create([
            'name' => 'موبایل و تبلت',
            'slug' => 'mobile-tablet',
            'description' => 'آگهی‌های خرید و فروش انواع موبایل و تبلت',
            'parent_id' => $electronics->id,
            'icon' => 'fa-mobile-alt',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'لپ تاپ و کامپیوتر',
            'slug' => 'laptop-computer',
            'description' => 'آگهی‌های خرید و فروش انواع لپ تاپ و کامپیوتر',
            'parent_id' => $electronics->id,
            'icon' => 'fa-laptop',
            'order' => 2,
        ]);

        Category::create([
            'name' => 'کنسول بازی و بازی ویدئویی',
            'slug' => 'gaming',
            'description' => 'آگهی‌های خرید و فروش انواع کنسول بازی و بازی ویدئویی',
            'parent_id' => $electronics->id,
            'icon' => 'fa-gamepad',
            'order' => 3,
        ]);

        Category::create([
            'name' => 'صوتی و تصویری',
            'slug' => 'audio-video',
            'description' => 'آگهی‌های خرید و فروش انواع لوازم صوتی و تصویری',
            'parent_id' => $electronics->id,
            'icon' => 'fa-tv',
            'order' => 4,
        ]);

        // Home & Kitchen Category
        $homeKitchen = Category::create([
            'name' => 'خانه و آشپزخانه',
            'slug' => 'home-kitchen',
            'description' => 'آگهی‌های خرید و فروش انواع لوازم خانه و آشپزخانه',
            'icon' => 'fa-couch',
            'is_featured' => true,
            'order' => 4,
            'meta_title' => 'آگهی‌های لوازم خانه و آشپزخانه | خرید و فروش',
            'meta_description' => 'آگهی‌های خرید و فروش انواع لوازم خانه و آشپزخانه، مبلمان، لوازم برقی و دکوراسیون',
            'meta_keywords' => 'لوازم خانگی، مبلمان، دکوراسیون، لوازم آشپزخانه',
        ]);

        // Home & Kitchen Subcategories
        Category::create([
            'name' => 'مبلمان و دکوراسیون',
            'slug' => 'furniture-decor',
            'description' => 'آگهی‌های خرید و فروش انواع مبلمان و دکوراسیون',
            'parent_id' => $homeKitchen->id,
            'icon' => 'fa-couch',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'لوازم برقی خانگی',
            'slug' => 'home-appliances',
            'description' => 'آگهی‌های خرید و فروش انواع لوازم برقی خانگی',
            'parent_id' => $homeKitchen->id,
            'icon' => 'fa-blender',
            'order' => 2,
        ]);

        Category::create([
            'name' => 'ظروف و لوازم آشپزخانه',
            'slug' => 'kitchenware',
            'description' => 'آگهی‌های خرید و فروش انواع ظروف و لوازم آشپزخانه',
            'parent_id' => $homeKitchen->id,
            'icon' => 'fa-utensils',
            'order' => 3,
        ]);

        // Services Category
        $services = Category::create([
            'name' => 'خدمات',
            'slug' => 'services',
            'description' => 'آگهی‌های ارائه انواع خدمات',
            'icon' => 'fa-concierge-bell',
            'is_featured' => true,
            'order' => 5,
            'meta_title' => 'آگهی‌های خدمات | ارائه انواع خدمات',
            'meta_description' => 'آگهی‌های ارائه انواع خدمات، تعمیرات، نظافت، آموزش و مشاوره',
            'meta_keywords' => 'خدمات، تعمیرات، نظافت، آموزش، مشاوره',
        ]);

        // Services Subcategories
        Category::create([
            'name' => 'تعمیرات',
            'slug' => 'repairs',
            'description' => 'آگهی‌های ارائه انواع خدمات تعمیرات',
            'parent_id' => $services->id,
            'icon' => 'fa-tools',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'نظافت و خدمات منزل',
            'slug' => 'home-services',
            'description' => 'آگهی‌های ارائه انواع خدمات نظافت و خدمات منزل',
            'parent_id' => $services->id,
            'icon' => 'fa-broom',
            'order' => 2,
        ]);

        Category::create([
            'name' => 'آموزش',
            'slug' => 'education',
            'description' => 'آگهی‌های ارائه انواع خدمات آموزشی',
            'parent_id' => $services->id,
            'icon' => 'fa-graduation-cap',
            'order' => 3,
        ]);

        Category::create([
            'name' => 'مشاوره',
            'slug' => 'consulting',
            'description' => 'آگهی‌های ارائه انواع خدمات مشاوره',
            'parent_id' => $services->id,
            'icon' => 'fa-comments',
            'order' => 4,
        ]);

        // Jobs Category
        $jobs = Category::create([
            'name' => 'استخدام و کاریابی',
            'slug' => 'jobs',
            'description' => 'آگهی‌های استخدام و کاریابی',
            'icon' => 'fa-briefcase',
            'is_featured' => true,
            'order' => 6,
            'meta_title' => 'آگهی‌های استخدام و کاریابی | فرصت‌های شغلی',
            'meta_description' => 'آگهی‌های استخدام و کاریابی، فرصت‌های شغلی و درخواست کار',
            'meta_keywords' => 'استخدام، کاریابی، فرصت شغلی، درخواست کار',
        ]);

        // Jobs Subcategories
        Category::create([
            'name' => 'استخدام',
            'slug' => 'hiring',
            'description' => 'آگهی‌های استخدام و فرصت‌های شغلی',
            'parent_id' => $jobs->id,
            'icon' => 'fa-user-plus',
            'order' => 1,
        ]);

        Category::create([
            'name' => 'درخواست کار',
            'slug' => 'job-seeking',
            'description' => 'آگهی‌های درخواست کار و رزومه',
            'parent_id' => $jobs->id,
            'icon' => 'fa-user-tie',
            'order' => 2,
        ]);
    }
}
