<?php

namespace Database\Seeders;

use App\Models\Ad;
use App\Models\AdImage;
use Illuminate\Database\Seeder;

class AdImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::all();
        
        // Sample image paths (you can replace these with actual image paths)
        $sampleImages = [
            'ads/sample1.jpg',
            'ads/sample2.jpg',
            'ads/sample3.jpg',
            'ads/sample4.jpg',
            'ads/sample5.jpg',
            'ads/sample6.jpg',
            'ads/sample7.jpg',
            'ads/sample8.jpg',
            'ads/sample9.jpg',
            'ads/sample10.jpg',
        ];
        
        foreach ($ads as $ad) {
            // Each ad gets 1-4 random images
            $imageCount = rand(1, 4);
            
            for ($i = 0; $i < $imageCount; $i++) {
                AdImage::create([
                    'ad_id' => $ad->id,
                    'image_path' => $sampleImages[array_rand($sampleImages)],
                    'is_primary' => $i === 0, // First image is primary
                    'order' => $i,
                ]);
            }
        }
    }
}
