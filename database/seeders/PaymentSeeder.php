<?php

namespace Database\Seeders;

use App\Models\Payment;
use App\Models\Ad;
use Illuminate\Database\Seeder;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::whereNotNull('package_id')->get();

        foreach ($ads as $ad) {
            // Create payment for ads with packages
            if ($ad->package && $ad->package->price > 0) {
                Payment::create([
                    'user_id' => $ad->user_id,
                    'ad_id' => $ad->id,
                    'amount' => $ad->package->price,
                    'status' => fake()->randomElement(['completed', 'pending', 'failed']),
                    'reference_id' => 'REF' . fake()->unique()->numberBetween(100000, 999999),
                    'transaction_id' => 'TXN' . fake()->unique()->numberBetween(1000000, 9999999),
                    'description' => 'پرداخت هزینه پکیج ' . $ad->package->name . ' برای آگهی: ' . $ad->title,
                    'created_at' => $ad->created_at,
                    'updated_at' => $ad->updated_at,
                ]);
            }
        }
    }
}
