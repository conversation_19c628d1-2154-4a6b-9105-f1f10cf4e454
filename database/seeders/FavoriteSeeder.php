<?php

namespace Database\Seeders;

use App\Models\Favorite;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Database\Seeder;

class FavoriteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::all();
        $users = User::all();

        // هر کاربر 3 تا 15 آگهی را به علاقه‌مندی‌هایش اضافه می‌کند
        foreach ($users as $user) {
            $favoriteCount = rand(3, 15);
            $availableAds = $ads->where('user_id', '!=', $user->id); // حذف آگهی‌های خود کاربر

            if ($availableAds->count() < $favoriteCount) {
                $favoriteCount = $availableAds->count();
            }

            $selectedAds = $availableAds->random($favoriteCount);

            foreach ($selectedAds as $ad) {
                try {
                    Favorite::create([
                        'user_id' => $user->id,
                        'ad_id' => $ad->id,
                        'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                        'updated_at' => fake()->dateTimeBetween('-30 days', 'now'),
                    ]);
                } catch (\Exception $e) {
                    // اگر duplicate entry باشد، ادامه بده
                    continue;
                }
            }
        }
    }
}
