<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', App\Livewire\HomePage::class)->name('home');
Route::get('/faq', function () {
    return view('publics.faq');
})->name('faq');
Route::get('/contact', function () {
    return view(view: 'publics.contact');
})->name('contact');

Route::get('/register', function () {
    return view('auth.register');
})->name('register');
Route::get('/login', function () {
    return view('auth.login');
})->name('login');
Route::get('/logout', function () {
    Auth::logout();
    session()->flash('success', 'با موفقیت از حساب خود خارج شدید');
    return redirect()->route('home');
})->name('logout');



// Category routes
Route::get('/categories', App\Livewire\CategoriesIndex::class)->name('categories.index');
Route::get('/category/{slug}', App\Livewire\CategoryShow::class)->name('category.show');

// Test route
Route::get('/test', App\Livewire\TestComponent::class)->name('test');

// Ad routes
Route::get('/ads', App\Livewire\ListAds::class)->name('ads.index');
Route::get('/ads/create', App\Livewire\CreateAd::class)->name('ads.create')->middleware('auth');
Route::get('/ads/{ad}', App\Livewire\ShowAd::class)->name('ads.show');
Route::get('/ads/{ad}/edit', App\Livewire\EditAd::class)->name('ads.edit')->middleware('auth');

// User routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', App\Livewire\UserDashboard::class)->name('user.dashboard');
    Route::get('/profile', App\Livewire\UserProfile::class)->name('user.profile');
    Route::get('/my-ads', App\Livewire\UserAds::class)->name('user.ads');
    Route::get('/my-payments', App\Livewire\UserPayments::class)->name('user.payments');
});

// Payment routes
Route::get('/payment/create/{ad}', App\Livewire\PaymentCreate::class)->name('payment.create')->middleware('auth');
Route::get('/payment/callback', App\Livewire\PaymentCallback::class)->name('payment.callback');

// Static pages
Route::get('/faq', function () {
    return view('pages.faq');
})->name('faq');

Route::get('/contact', function () {
    return view('pages.contact');
})->name('contact');

// Admin routes
Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'admin']], function () {
    // Dashboard
    Route::get('/', App\Livewire\Admin\Dashboard::class)->name('admin.dashboard');

    // Categories Management
    Route::get('/categories', App\Livewire\Admin\Categories\Index::class)->name('admin.categories.index');
    Route::get('/categories/create', App\Livewire\Admin\Categories\Create::class)->name('admin.categories.create');
    Route::get('/categories/{category}/edit', App\Livewire\Admin\Categories\Edit::class)->name('admin.categories.edit');

    // Ads Management
    Route::get('/ads', App\Livewire\Admin\Ads\Index::class)->name('admin.ads.index');
    Route::get('/ads/create', App\Livewire\Admin\Ads\Create::class)->name('admin.ads.create');
    Route::get('/ads/{ad}/edit', App\Livewire\Admin\Ads\Edit::class)->name('admin.ads.edit');

    // Users Management
    Route::get('/users', App\Livewire\Admin\Users\Index::class)->name('admin.users.index');
    Route::get('/users/create', App\Livewire\Admin\Users\Create::class)->name('admin.users.create');
    Route::get('/users/{user}/edit', App\Livewire\Admin\Users\Edit::class)->name('admin.users.edit');

    // Packages Management
    Route::get('/packages', App\Livewire\Admin\Packages\Index::class)->name('admin.packages.index');
    Route::get('/packages/create', App\Livewire\Admin\Packages\Create::class)->name('admin.packages.create');
    Route::get('/packages/{package}/edit', App\Livewire\Admin\Packages\Edit::class)->name('admin.packages.edit');

    // Payments Management
    Route::get('/payments', App\Livewire\Admin\Payments\Index::class)->name('admin.payments.index');
    Route::get('/payments/{payment}', App\Livewire\Admin\Payments\Show::class)->name('admin.payments.show');

    // Settings
    Route::get('/settings', App\Livewire\Admin\Settings\Index::class)->name('admin.settings.index');
});

