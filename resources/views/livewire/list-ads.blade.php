<div>
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="mb-0">
                <i class="fas fa-bullhorn me-2 text-primary"></i>
                لیست آگهی‌ها
            </h3>
            @if(auth()->check())
            <a href="{{ route('ads.create') }}" class="btn btn-gradient-primary pulse">
                <i class="fas fa-plus me-1"></i>
                ثبت آگهی جدید
            </a>
            @endif
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" wire:model.live="search" class="form-control border-start-0" placeholder="جستجو در آگهی‌ها...">
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-tags text-muted"></i>
                        </span>
                        <select wire:model.live="categoryFilter" class="form-select border-start-0">
                            <option value="">همه دسته‌بندی‌ها</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-sort text-muted"></i>
                        </span>
                        <select wire:model.live="sortField" class="form-select border-start-0">
                            <option value="created_at">تاریخ ثبت</option>
                            <option value="price">قیمت</option>
                            <option value="title">عنوان</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-arrow-up-down text-muted"></i>
                        </span>
                        <select wire:model.live="sortDirection" class="form-select border-start-0">
                            <option value="desc">نزولی</option>
                            <option value="asc">صعودی</option>
                        </select>
                    </div>
                </div>
            </div>

            @if($ads->count() > 0)
                <div class="row">
                    @foreach($ads as $ad)
                        <div class="col-md-4 mb-4 animate__animated animate__fadeInUp" style="animation-delay: {{ $loop->index * 0.1 }}s">
                            <div class="card h-100 position-relative {{ $ad->is_premium ? 'border-warning' : ($ad->is_featured ? 'border-info' : '') }}">
                                @if($ad->is_premium)
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-crown me-1"></i>پرمیوم
                                        </span>
                                    </div>
                                @elseif($ad->is_featured)
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-info">
                                            <i class="fas fa-star me-1"></i>ویژه
                                        </span>
                                    </div>
                                @endif

                                <div class="card-header border-0 bg-transparent">
                                    <h5 class="mb-0">
                                        <a href="{{ route('ads.show', $ad) }}" class="text-decoration-none text-dark fw-bold">
                                            {{ $ad->title }}
                                        </a>
                                    </h5>
                                </div>

                                <div class="card-body pt-0">
                                    <p class="text-muted mb-3">{{ \Illuminate\Support\Str::limit($ad->description, 80) }}</p>

                                    <div class="row g-2 mb-3">
                                        <div class="col-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-tag text-success me-2"></i>
                                                <strong class="text-success">{{ number_format($ad->price) }} تومان</strong>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                                <span class="text-muted">{{ $ad->location }}</span>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-folder text-primary me-2"></i>
                                                <span class="text-muted">{{ $ad->category->name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent border-0 d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $ad->created_at->format('Y/m/d') }}
                                    </small>

                                    <a href="{{ route('ads.show', $ad) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        مشاهده
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-3">
                    {{ $ads->links() }}
                </div>
            @else
                <div class="alert alert-info">
                    هیچ آگهی‌ای یافت نشد.
                </div>
            @endif
        </div>
    </div>
</div>
