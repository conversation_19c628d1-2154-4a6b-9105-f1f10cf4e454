<div>
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb bg-light p-3 rounded">
            <li class="breadcrumb-item">
                <a href="{{ route('home') }}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>خانه
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-bullhorn me-1"></i>آگهی‌ها
            </li>
        </ol>
    </nav>

    <!-- Page Header with Statistics -->
    <div class="card mb-4 animate__animated animate__fadeInUp">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2 text-primary">
                        <i class="fas fa-bullhorn me-2"></i>
                        آگهی‌های بیست رنگ
                    </h1>
                    <p class="text-muted mb-0">جستجو و مرور آگهی‌های متنوع در دسته‌بندی‌های مختلف</p>
                </div>
                <div class="col-md-4 text-end">
                    @if(auth()->check())
                        <a href="{{ route('ads.create') }}" class="btn btn-gradient-primary btn-lg pulse">
                            <i class="fas fa-plus me-2"></i>
                            ثبت آگهی جدید
                        </a>
                    @else
                        <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            ورود برای ثبت آگهی
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                <div class="card-body text-center">
                    <i class="fas fa-bullhorn fa-2x mb-2"></i>
                    <h4 class="mb-0">{{ $totalAds }}</h4>
                    <small>کل آگهی‌ها</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-2x mb-2"></i>
                    <h4 class="mb-0">{{ $featuredAds }}</h4>
                    <small>آگهی‌های ویژه</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x mb-2"></i>
                    <h4 class="mb-0">{{ $premiumAds }}</h4>
                    <small>آگهی‌های پرمیوم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                <div class="card-body text-center">
                    <i class="fas fa-tags fa-2x mb-2"></i>
                    <h4 class="mb-0">{{ $totalCategories }}</h4>
                    <small>دسته‌بندی‌ها</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar with Categories -->
        <div class="col-lg-3 mb-4">
            <!-- Mobile Filter Toggle -->
            <div class="d-lg-none mb-3">
                <button class="btn btn-primary w-100" type="button" data-bs-toggle="collapse" data-bs-target="#filterSidebar" aria-expanded="false" aria-controls="filterSidebar">
                    <i class="fas fa-filter me-2"></i>
                    فیلترها و جستجو
                    <i class="fas fa-chevron-down ms-2"></i>
                </button>
            </div>

            <div class="card animate__animated animate__fadeInRight collapse d-lg-block" id="filterSidebar">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فیلترها
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Search -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-search me-1"></i>جستجو
                        </label>
                        <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو در آگهی‌ها...">
                    </div>

                    <!-- Categories -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-tags me-1"></i>دسته‌بندی
                        </label>
                        <select wire:model.live="categoryFilter" class="form-select">
                            <option value="">همه دسته‌بندی‌ها</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Sort Options -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sort me-1"></i>مرتب‌سازی
                        </label>
                        <select wire:model.live="sortField" class="form-select mb-2">
                            <option value="created_at">تاریخ ثبت</option>
                            <option value="price">قیمت</option>
                            <option value="title">عنوان</option>
                        </select>
                        <select wire:model.live="sortDirection" class="form-select">
                            <option value="desc">نزولی</option>
                            <option value="asc">صعودی</option>
                        </select>
                    </div>

                    <!-- Quick Category Links -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-bolt me-1"></i>دسترسی سریع
                        </label>
                        <div class="d-grid gap-2">
                            @foreach($categories->take(6) as $category)
                                <button wire:click="$set('categoryFilter', '{{ $category->id }}')"
                                        class="btn btn-outline-primary btn-sm text-start">
                                    @if($category->icon)
                                        <i class="fas {{ $category->icon }} me-2"></i>
                                    @endif
                                    {{ $category->name }}
                                    <span class="badge bg-secondary float-end">{{ $category->getAllAdsCount() }}</span>
                                </button>
                            @endforeach
                            <a href="{{ route('categories.index') }}" class="btn btn-link btn-sm">
                                مشاهده همه دسته‌بندی‌ها
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Clear Filters -->
                    @if($search || $categoryFilter)
                        <div class="mb-3">
                            <button wire:click="clearFilters" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-times me-1"></i>
                                پاک کردن فیلترها
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <div class="card animate__animated animate__fadeInUp">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        نتایج جستجو
                        @if($search || $categoryFilter)
                            <small class="text-muted">
                                ({{ $ads->total() }} آگهی یافت شد)
                            </small>
                        @endif
                    </h5>
                    <div class="d-flex align-items-center">
                        <label class="form-label me-2 mb-0">نمایش:</label>
                        <select wire:model.live="perPage" class="form-select form-select-sm" style="width: auto;">
                            <option value="9">9</option>
                            <option value="18">18</option>
                            <option value="36">36</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    @if($ads->count() > 0)
                <div class="row">
                    @foreach($ads as $ad)
                        <div class="col-sm-6 col-lg-4 mb-4 animate__animated animate__fadeInUp" style="animation-delay: {{ $loop->index * 0.1 }}s">
                            <div class="card h-100 position-relative {{ $ad->is_premium ? 'border-warning' : ($ad->is_featured ? 'border-info' : '') }}">
                                @if($ad->is_premium)
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-crown me-1"></i>پرمیوم
                                        </span>
                                    </div>
                                @elseif($ad->is_featured)
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-info">
                                            <i class="fas fa-star me-1"></i>ویژه
                                        </span>
                                    </div>
                                @endif

                                <div class="card-header border-0 bg-transparent">
                                    <h5 class="mb-0">
                                        <a href="{{ route('ads.show', $ad) }}" class="text-decoration-none text-dark fw-bold">
                                            {{ $ad->title }}
                                        </a>
                                    </h5>
                                </div>

                                <div class="card-body pt-0">
                                    <p class="text-muted mb-3">{{ \Illuminate\Support\Str::limit($ad->description, 80) }}</p>

                                    <div class="row g-2 mb-3">
                                        <div class="col-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-tag text-success me-2"></i>
                                                <strong class="text-success">{{ number_format($ad->price) }} تومان</strong>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                                <span class="text-muted">{{ $ad->location }}</span>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-folder text-primary me-2"></i>
                                                <span class="text-muted">{{ $ad->category->name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent border-0 d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $ad->created_at->format('Y/m/d') }}
                                    </small>

                                    <a href="{{ route('ads.show', $ad) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        مشاهده
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                        <div class="mt-4">
                            {{ $ads->links() }}
                        </div>
                    @else
                        <div class="alert alert-info text-center">
                            <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                            <h5>هیچ آگهی‌ای یافت نشد</h5>
                            <p class="text-muted">لطفاً فیلترهای جستجو را تغییر دهید یا آگهی جدید ثبت کنید.</p>
                            @if(auth()->check())
                                <a href="{{ route('ads.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>ثبت آگهی جدید
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
