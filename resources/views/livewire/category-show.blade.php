<div>
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">خانه</a></li>
                        @if($category->parent)
                            <li class="breadcrumb-item"><a href="{{ route('category.show', $category->parent->slug) }}">{{ $category->parent->name }}</a></li>
                        @endif
                        <li class="breadcrumb-item active" aria-current="page">{{ $category->name }}</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h1 class="h3 mb-0">
                            @if($category->icon)
                                <i class="fas {{ $category->icon }} me-2"></i>
                            @endif
                            {{ $category->name }}
                        </h1>
                        <a href="{{ route('ads.create') }}" class="btn btn-light">ثبت آگهی در این دسته</a>
                    </div>
                    <div class="card-body">
                        @if($category->description)
                            <p class="lead">{{ $category->description }}</p>
                        @endif

                        @if($category->children->count() > 0)
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h3 class="border-bottom pb-2 mb-3">زیر دسته‌ها</h3>
                                    <div class="row">
                                        @foreach($category->children as $child)
                                            <div class="col-md-3 mb-3">
                                                <a href="{{ route('category.show', $child->slug) }}" class="card h-100 text-decoration-none">
                                                    <div class="card-body text-center">
                                                        @if($child->icon)
                                                            <i class="fas {{ $child->icon }} fa-3x mb-3 text-primary"></i>
                                                        @endif
                                                        <h5 class="card-title">{{ $child->name }}</h5>
                                                        <p class="card-text text-muted small">{{ $child->ads_count }} آگهی</p>
                                                    </div>
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو در {{ $category->name }}...">
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="sortField" class="form-control">
                                    <option value="created_at">تاریخ ثبت</option>
                                    <option value="price">قیمت</option>
                                    <option value="title">عنوان</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="sortDirection" class="form-control">
                                    <option value="desc">نزولی</option>
                                    <option value="asc">صعودی</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select wire:model.live="perPage" class="form-control">
                                    <option value="12">12</option>
                                    <option value="24">24</option>
                                    <option value="36">36</option>
                                </select>
                            </div>
                        </div>

                        @if($ads->count() > 0)
                            <div class="row">
                                @foreach($ads as $ad)
                                    <div class="col-md-4 mb-4">
                                        <div class="card h-100 {{ $ad->is_premium ? 'border-warning' : ($ad->is_featured ? 'border-info' : '') }}">
                                            <div class="card-header {{ $ad->is_premium ? 'bg-warning' : ($ad->is_featured ? 'bg-info' : '') }}">
                                                <h5 class="mb-0">
                                                    <a href="{{ route('ads.show', $ad) }}" class="text-decoration-none {{ $ad->is_premium || $ad->is_featured ? 'text-white' : '' }}">
                                                        {{ $ad->title }}
                                                    </a>
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <p>{{ Str::limit($ad->description, 100) }}</p>
                                                <p><strong>قیمت:</strong> {{ number_format($ad->price) }} تومان</p>
                                                <p><strong>موقعیت:</strong> {{ $ad->location }}</p>
                                            </div>
                                            <div class="card-footer d-flex justify-content-between align-items-center">
                                                <small class="text-muted">{{ $ad->created_at->diffForHumans() }}</small>
                                                <a href="{{ route('ads.show', $ad) }}" class="btn btn-sm {{ $ad->is_premium ? 'btn-warning' : ($ad->is_featured ? 'btn-info' : 'btn-primary') }}">
                                                    مشاهده آگهی
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-4">
                                {{ $ads->links('custom.pagination') }}
                            </div>
                        @else
                            <div class="alert alert-info">
                                هیچ آگهی‌ای در این دسته‌بندی یافت نشد.
                                <a href="{{ route('ads.create') }}" class="btn btn-primary ms-3">ثبت آگهی جدید</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        @if($relatedCategories->count() > 0)
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h3 class="mb-0">دسته‌بندی‌های مرتبط</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($relatedCategories as $relatedCategory)
                                    <div class="col-md-3 mb-3">
                                        <a href="{{ route('category.show', $relatedCategory->slug) }}" class="btn btn-outline-info w-100">
                                            @if($relatedCategory->icon)
                                                <i class="fas {{ $relatedCategory->icon }} me-2"></i>
                                            @endif
                                            {{ $relatedCategory->name }}
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
