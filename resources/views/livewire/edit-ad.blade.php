<div>
    <div class="card">
        <div class="card-header">
            <h3>ویرایش آگهی</h3>
        </div>
        <div class="card-body">
            <form wire:submit.prevent="update">
                <div class="mb-3">
                    <label for="title" class="form-label">عنوان آگهی</label>
                    <input type="text" class="form-control" id="title" wire:model="title">
                    @error('title') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">توضیحات</label>
                    <textarea class="form-control" id="description" rows="5" wire:model="description"></textarea>
                    @error('description') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                <div class="mb-3">
                    <label for="price" class="form-label">قیمت (تومان)</label>
                    <input type="number" class="form-control" id="price" wire:model="price">
                    @error('price') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                <div class="mb-3">
                    <label for="location" class="form-label">موقعیت</label>
                    <input type="text" class="form-control" id="location" wire:model="location">
                    @error('location') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                <div class="mb-3">
                    <label for="category_id" class="form-label">دسته‌بندی</label>
                    <select class="form-control" id="category_id" wire:model="category_id">
                        <option value="">انتخاب دسته‌بندی</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                        @endforeach
                    </select>
                    @error('category_id') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                <button type="submit" class="btn btn-primary">بروزرسانی آگهی</button>
            </form>
        </div>
    </div>
</div>
