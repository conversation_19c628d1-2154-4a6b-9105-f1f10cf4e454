<div>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                @include('sections.user-sidebar')
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h3>آگهی‌های من</h3>
                        <a href="{{ route('ads.create') }}" class="btn btn-light">ثبت آگهی جدید</a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="statusFilter" class="form-control">
                                    <option value="">همه وضعیت‌ها</option>
                                    <option value="active">فعال</option>
                                    <option value="expired">منقضی شده</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="categoryFilter" class="form-control">
                                    <option value="">همه دسته‌بندی‌ها</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select wire:model.live="perPage" class="form-control">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                        </div>

                        @if($ads->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>عنوان</th>
                                            <th>دسته‌بندی</th>
                                            <th>قیمت</th>
                                            <th>نوع</th>
                                            <th>وضعیت</th>
                                            <th>تاریخ ثبت</th>
                                            <th>تاریخ انقضا</th>
                                            <th>عملیات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($ads as $ad)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('ads.show', $ad) }}">{{ $ad->title }}</a>
                                                </td>
                                                <td>{{ $ad->category->name }}</td>
                                                <td>{{ number_format($ad->price) }}</td>
                                                <td>
                                                    @if($ad->is_premium)
                                                        <span class="badge bg-warning">ویژه</span>
                                                    @elseif($ad->is_featured)
                                                        <span class="badge bg-info">برجسته</span>
                                                    @elseif($ad->type == 'ladder')
                                                        <span class="badge bg-primary">نردبانی</span>
                                                    @else
                                                        <span class="badge bg-secondary">عادی</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($ad->expires_at && $ad->expires_at->isPast())
                                                        <span class="badge bg-danger">منقضی شده</span>
                                                    @else
                                                        <span class="badge bg-success">فعال</span>
                                                    @endif
                                                </td>
                                                <td>{{ $ad->created_at->format('Y/m/d') }}</td>
                                                <td>{{ $ad->expires_at ? $ad->expires_at->format('Y/m/d') : 'نامحدود' }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ route('ads.show', $ad) }}" class="btn btn-info">مشاهده</a>
                                                        <a href="{{ route('ads.edit', $ad) }}" class="btn btn-primary">ویرایش</a>
                                                        @if($ad->expires_at && $ad->expires_at->isPast())
                                                            <button wire:click="renewAd({{ $ad->id }})" class="btn btn-success">تمدید</button>
                                                        @endif
                                                        <button wire:click="deleteAd({{ $ad->id }})" class="btn btn-danger" onclick="return confirm('آیا از حذف این آگهی اطمینان دارید؟')">حذف</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="mt-3">
                                {{ $ads->links() }}
                            </div>
                        @else
                            <div class="alert alert-info">
                                هیچ آگهی‌ای یافت نشد.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
