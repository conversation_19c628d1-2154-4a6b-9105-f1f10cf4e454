<div>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                @include('sections.user-sidebar')
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>تاریخچه پرداخت‌های من</h3>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="statusFilter" class="form-control">
                                    <option value="">همه وضعیت‌ها</option>
                                    <option value="completed">موفق</option>
                                    <option value="pending">در انتظار</option>
                                    <option value="failed">ناموفق</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select wire:model.live="sortField" class="form-control">
                                    <option value="created_at">تاریخ</option>
                                    <option value="amount">مبلغ</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select wire:model.live="perPage" class="form-control">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                        </div>

                        @if($payments->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>شماره پیگیری</th>
                                            <th>آگهی</th>
                                            <th>مبلغ (تومان)</th>
                                            <th>وضعیت</th>
                                            <th>توضیحات</th>
                                            <th>تاریخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($payments as $payment)
                                            <tr>
                                                <td>{{ $payment->reference_id ?? 'نامشخص' }}</td>
                                                <td>
                                                    @if($payment->ad)
                                                        <a href="{{ route('ads.show', $payment->ad) }}">{{ $payment->ad->title }}</a>
                                                    @else
                                                        آگهی حذف شده
                                                    @endif
                                                </td>
                                                <td>{{ number_format($payment->amount) }}</td>
                                                <td>
                                                    @if($payment->status == 'completed')
                                                        <span class="badge bg-success">موفق</span>
                                                    @elseif($payment->status == 'pending')
                                                        <span class="badge bg-warning">در انتظار</span>
                                                    @else
                                                        <span class="badge bg-danger">ناموفق</span>
                                                    @endif
                                                </td>
                                                <td>{{ $payment->description }}</td>
                                                <td>{{ $payment->created_at->format('Y/m/d H:i') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="mt-3">
                                {{ $payments->links() }}
                            </div>
                        @else
                            <div class="alert alert-info">
                                هیچ پرداختی یافت نشد.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
