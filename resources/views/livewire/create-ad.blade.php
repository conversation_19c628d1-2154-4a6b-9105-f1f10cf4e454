<div>
    <div class="container py-4">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">ثبت آگهی جدید</h3>
                    </div>
                    <div class="card-body">
                        <form wire:submit.prevent="save">
                            <!-- Ad Title -->
                            <div class="mb-4">
                                <label for="title" class="form-label fw-bold">عنوان آگهی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" wire:model="title" placeholder="عنوان جذاب و مختصر برای آگهی خود وارد کنید">
                                @error('title') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                            </div>

                            <!-- Category Selection -->
                            <div class="mb-4">
                                <label for="category_id" class="form-label fw-bold">دسته‌بندی <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" wire:model="category_id">
                                    <option value="">انتخاب دسته‌بندی</option>
                                    @foreach($parentCategories as $parentCategory)
                                        <optgroup label="{{ $parentCategory->name }}">
                                            <option value="{{ $parentCategory->id }}">{{ $parentCategory->name }}</option>
                                            @foreach($parentCategory->children as $childCategory)
                                                <option value="{{ $childCategory->id }}">-- {{ $childCategory->name }}</option>
                                            @endforeach
                                        </optgroup>
                                    @endforeach
                                </select>
                                @error('category_id') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                            </div>

                            <!-- Ad Description -->
                            <div class="mb-4">
                                <label for="description" class="form-label fw-bold">توضیحات <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" rows="6" wire:model="description" placeholder="توضیحات کامل آگهی را وارد کنید. هرچه توضیحات دقیق‌تر باشد، شانس فروش بیشتر است."></textarea>
                                @error('description') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                            </div>

                            <!-- Price and Location -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="price" class="form-label fw-bold">قیمت (تومان) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" wire:model="price" placeholder="مثال: 1500000">
                                        <span class="input-group-text">تومان</span>
                                    </div>
                                    @error('price') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="location" class="form-label fw-bold">موقعیت <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location" wire:model="location" placeholder="مثال: تهران، سعادت آباد">
                                    @error('location') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                                </div>
                            </div>

                            <!-- Image Upload -->
                            <div class="mb-4">
                                <label class="form-label fw-bold">تصاویر آگهی <span class="text-danger">*</span></label>
                                <div class="mb-2 small text-muted">حداقل یک تصویر و حداکثر 5 تصویر انتخاب کنید. فرمت‌های مجاز: JPG, PNG, JPEG. حداکثر حجم هر تصویر: 2MB</div>

                                <div class="mb-3">
                                    <input type="file" class="form-control" id="images" wire:model="images" multiple accept="image/jpeg,image/png,image/jpg">
                                    <div wire:loading wire:target="images" class="text-primary mt-2 small">
                                        <i class="fas fa-spinner fa-spin"></i> در حال آپلود تصاویر...
                                    </div>
                                    @error('images') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                                    @error('images.*') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                                </div>

                                @if(count($images) > 0)
                                    <div class="row">
                                        @foreach($images as $index => $image)
                                            <div class="col-md-3 col-sm-4 col-6 mb-3">
                                                <div class="position-relative">
                                                    <img src="{{ $image->temporaryUrl() }}" class="img-thumbnail" style="width: 100%; height: 120px; object-fit: cover;">
                                                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0" wire:click="removeImage({{ $index }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <div class="form-check mt-1">
                                                        <input class="form-check-input" type="radio" name="primaryImage" id="primaryImage{{ $index }}" value="{{ $index }}" wire:model="primaryImage">
                                                        <label class="form-check-label small" for="primaryImage{{ $index }}">
                                                            تصویر اصلی
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>

                            <!-- Package Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-bold">نوع آگهی</label>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 {{ $package_id ? '' : 'border-primary' }}">
                                            <div class="card-header {{ $package_id ? 'bg-light' : 'bg-primary text-white' }}">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="packageType" id="packageFree" wire:model="package_id" value="">
                                                    <label class="form-check-label fw-bold" for="packageFree">
                                                        آگهی رایگان
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <p class="card-text">آگهی شما به صورت عادی نمایش داده می‌شود.</p>
                                                <ul class="list-unstyled mb-0">
                                                    <li><i class="fas fa-check text-success me-2"></i> نمایش به مدت 30 روز</li>
                                                    <li><i class="fas fa-check text-success me-2"></i> رایگان</li>
                                                    <li><i class="fas fa-times text-danger me-2"></i> بدون اولویت نمایش</li>
                                                </ul>
                                            </div>
                                            <div class="card-footer bg-white">
                                                <div class="text-center fw-bold">رایگان</div>
                                            </div>
                                        </div>
                                    </div>

                                    @foreach($packages as $package)
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100 {{ $package_id == $package->id ? 'border-primary' : '' }}">
                                                <div class="card-header {{ $package_id == $package->id ? 'bg-primary text-white' : 'bg-light' }}">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="packageType" id="package{{ $package->id }}" wire:model="package_id" value="{{ $package->id }}">
                                                        <label class="form-check-label fw-bold" for="package{{ $package->id }}">
                                                            {{ $package->name }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text">{{ $package->description }}</p>
                                                    <ul class="list-unstyled mb-0">
                                                        <li><i class="fas fa-check text-success me-2"></i> نمایش به مدت {{ $package->duration_days }} روز</li>
                                                        @if($package->is_featured)
                                                            <li><i class="fas fa-check text-success me-2"></i> نمایش در بخش آگهی‌های برجسته</li>
                                                        @endif
                                                        @if($package->is_premium)
                                                            <li><i class="fas fa-check text-success me-2"></i> نمایش در بخش آگهی‌های ویژه</li>
                                                        @endif
                                                        @if($package->name === 'نردبانی')
                                                            <li><i class="fas fa-check text-success me-2"></i> نمایش دوره‌ای در بالای لیست</li>
                                                        @endif
                                                    </ul>
                                                </div>
                                                <div class="card-footer bg-white">
                                                    <div class="text-center fw-bold">{{ number_format($package->price) }} تومان</div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                @error('package_id') <div class="text-danger mt-1 small">{{ $message }}</div> @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ route('ads.index') }}" class="btn btn-secondary">انصراف</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> ثبت آگهی
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">راهنمای ثبت آگهی</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="fw-bold">نکات مهم:</h6>
                        <ul class="small">
                            <li>عنوان آگهی باید گویا و مختصر باشد.</li>
                            <li>توضیحات کامل و دقیق باشد.</li>
                            <li>حتماً تصویر باکیفیت از کالا یا خدمات خود ارائه دهید.</li>
                            <li>قیمت واقعی را ذکر کنید.</li>
                        </ul>

                        <h6 class="fw-bold mt-3">مزایای آگهی‌های ویژه:</h6>
                        <ul class="small">
                            <li>نمایش در بالای لیست آگهی‌ها</li>
                            <li>نمایش در صفحه اصلی سایت</li>
                            <li>افزایش چشمگیر بازدید آگهی</li>
                            <li>افزایش شانس فروش تا 5 برابر</li>
                        </ul>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0">قوانین انتشار آگهی</h5>
                    </div>
                    <div class="card-body">
                        <ul class="small">
                            <li>انتشار آگهی‌های غیرقانونی ممنوع است.</li>
                            <li>استفاده از تصاویر نامناسب ممنوع است.</li>
                            <li>درج شماره تماس و آدرس وب‌سایت در متن آگهی ممنوع است.</li>
                            <li>آگهی‌های تکراری حذف خواهند شد.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
