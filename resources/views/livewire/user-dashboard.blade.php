<div>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                @include('sections.user-sidebar')
            </div>
            <div class="col-md-9">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h3>داشبورد کاربری</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h1 class="display-4">{{ $totalAds }}</h1>
                                        <p class="lead">آگهی‌های من</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h1 class="display-4">{{ $activeAds }}</h1>
                                        <p class="lead">آگهی‌های فعال</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h1 class="display-4">{{ $payments }}</h1>
                                        <p class="lead">پرداخت‌های موفق</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h4>آخرین آگهی‌های من</h4>
                        <a href="{{ route('ads.create') }}" class="btn btn-light">ثبت آگهی جدید</a>
                    </div>
                    <div class="card-body">
                        @if($latestAds->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>عنوان</th>
                                            <th>دسته‌بندی</th>
                                            <th>قیمت</th>
                                            <th>نوع</th>
                                            <th>وضعیت</th>
                                            <th>تاریخ ثبت</th>
                                            <th>عملیات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($latestAds as $ad)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('ads.show', $ad) }}">{{ $ad->title }}</a>
                                                </td>
                                                <td>{{ $ad->category->name }}</td>
                                                <td>{{ number_format($ad->price) }}</td>
                                                <td>
                                                    @if($ad->is_premium)
                                                        <span class="badge bg-warning">ویژه</span>
                                                    @elseif($ad->is_featured)
                                                        <span class="badge bg-info">برجسته</span>
                                                    @elseif($ad->type == 'ladder')
                                                        <span class="badge bg-primary">نردبانی</span>
                                                    @else
                                                        <span class="badge bg-secondary">عادی</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($ad->expires_at && $ad->expires_at->isPast())
                                                        <span class="badge bg-danger">منقضی شده</span>
                                                    @else
                                                        <span class="badge bg-success">فعال</span>
                                                    @endif
                                                </td>
                                                <td>{{ $ad->created_at->format('Y/m/d') }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ route('ads.show', $ad) }}" class="btn btn-info">مشاهده</a>
                                                        <a href="{{ route('ads.edit', $ad) }}" class="btn btn-primary">ویرایش</a>
                                                        @if($ad->expires_at && $ad->expires_at->isPast())
                                                            <button wire:click="renewAd({{ $ad->id }})" class="btn btn-success">تمدید</button>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ route('user.ads') }}" class="btn btn-primary">مشاهده همه آگهی‌های من</a>
                            </div>
                        @else
                            <div class="alert alert-info">
                                شما هنوز هیچ آگهی ثبت نکرده‌اید.
                                <a href="{{ route('ads.create') }}" class="btn btn-primary ms-3">ثبت آگهی جدید</a>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h4>آخرین پرداخت‌های من</h4>
                    </div>
                    <div class="card-body">
                        @if($latestPayments->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>شماره پیگیری</th>
                                            <th>آگهی</th>
                                            <th>مبلغ (تومان)</th>
                                            <th>وضعیت</th>
                                            <th>تاریخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($latestPayments as $payment)
                                            <tr>
                                                <td>{{ $payment->reference_id ?? 'نامشخص' }}</td>
                                                <td>
                                                    @if($payment->ad)
                                                        <a href="{{ route('ads.show', $payment->ad) }}">{{ $payment->ad->title }}</a>
                                                    @else
                                                        آگهی حذف شده
                                                    @endif
                                                </td>
                                                <td>{{ number_format($payment->amount) }}</td>
                                                <td>
                                                    @if($payment->status == 'completed')
                                                        <span class="badge bg-success">موفق</span>
                                                    @elseif($payment->status == 'pending')
                                                        <span class="badge bg-warning">در انتظار</span>
                                                    @else
                                                        <span class="badge bg-danger">ناموفق</span>
                                                    @endif
                                                </td>
                                                <td>{{ $payment->created_at->format('Y/m/d H:i') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ route('user.payments') }}" class="btn btn-warning">مشاهده همه پرداخت‌های من</a>
                            </div>
                        @else
                            <div class="alert alert-info">
                                شما هنوز هیچ پرداختی انجام نداده‌اید.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
