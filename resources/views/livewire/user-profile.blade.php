<div>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                @include('sections.user-sidebar')
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>پروفایل کاربری</h3>
                    </div>
                    <div class="card-body">
                        <form wire:submit.prevent="updateProfile">
                            <div class="row">
                                <div class="col-md-4 text-center mb-4">
                                    @if($avatar)
                                        <img src="{{ $avatar->temporaryUrl() }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                    @elseif($user->avatar)
                                        <img src="{{ asset('storage/' . $user->avatar) }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                    @else
                                        <img src="{{ asset('images/default-avatar.png') }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                    @endif
                                    
                                    <div class="mb-3">
                                        <label for="avatar" class="form-label">تصویر پروفایل</label>
                                        <input type="file" class="form-control" id="avatar" wire:model="avatar" accept="image/*">
                                        @error('avatar') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">نام و نام خانوادگی</label>
                                        <input type="text" class="form-control" id="name" wire:model="name">
                                        @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ایمیل</label>
                                        <input type="email" class="form-control" id="email" wire:model="email">
                                        @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="mobile" class="form-label">شماره موبایل</label>
                                        <input type="text" class="form-control" id="mobile" wire:model="mobile" readonly>
                                        <small class="text-muted">شماره موبایل قابل تغییر نیست</small>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="mb-3">
                                <h5>تغییر رمز عبور</h5>
                                <p class="text-muted">اگر نمی‌خواهید رمز عبور خود را تغییر دهید، این قسمت را خالی بگذارید.</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">رمز عبور فعلی</label>
                                        <input type="password" class="form-control" id="current_password" wire:model="current_password">
                                        @error('current_password') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">رمز عبور جدید</label>
                                        <input type="password" class="form-control" id="password" wire:model="password">
                                        @error('password') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">تکرار رمز عبور جدید</label>
                                        <input type="password" class="form-control" id="password_confirmation" wire:model="password_confirmation">
                                        @error('password_confirmation') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">بروزرسانی پروفایل</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
