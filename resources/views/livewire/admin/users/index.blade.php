<div>
    @section('page-title', 'مدیریت کاربران')

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold">لیست کاربران</h6>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> افزودن کاربر جدید
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3">
                    <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                </div>
                <div class="col-md-2">
                    <select wire:model.live="roleFilter" class="form-control">
                        <option value="">همه نقش‌ها</option>
                        <option value="user">کاربر</option>
                        <option value="admin">مدیر</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="statusFilter" class="form-control">
                        <option value="">همه وضعیت‌ها</option>
                        <option value="1">فعال</option>
                        <option value="0">غیرفعال</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="perPage" class="form-control">
                        <option value="10">10 مورد</option>
                        <option value="25">25 مورد</option>
                        <option value="50">50 مورد</option>
                        <option value="100">100 مورد</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">ردیف</th>
                            <th wire:click="sortBy('name')" style="cursor: pointer;">
                                نام
                                @if($sortField === 'name')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th wire:click="sortBy('mobile')" style="cursor: pointer;">
                                موبایل
                                @if($sortField === 'mobile')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th>ایمیل</th>
                            <th>نقش</th>
                            <th>تعداد آگهی</th>
                            <th>وضعیت</th>
                            <th wire:click="sortBy('created_at')" style="cursor: pointer;">
                                تاریخ ثبت
                                @if($sortField === 'created_at')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th style="width: 150px;">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $user->name ?: 'بدون نام' }}</td>
                                <td>{{ $user->mobile }}</td>
                                <td>{{ $user->email ?: '-' }}</td>
                                <td>
                                    @if($user->role === 'admin')
                                        <span class="badge bg-danger">مدیر</span>
                                    @else
                                        <span class="badge bg-primary">کاربر</span>
                                    @endif
                                </td>
                                <td>{{ $user->ads_count }}</td>
                                <td>
                                    @if($user->is_active)
                                        <span class="badge bg-success">فعال</span>
                                    @else
                                        <span class="badge bg-secondary">غیرفعال</span>
                                    @endif
                                </td>
                                <td>{{ $user->created_at->format('Y/m/d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <button type="button" class="btn btn-sm btn-{{ $user->is_active ? 'warning' : 'success' }}"
                                                wire:click="toggleUserStatus({{ $user->id }})">
                                            <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }}"></i>
                                        </button>

                                        <button type="button" class="btn btn-sm btn-danger"
                                                wire:click="confirmDelete({{ $user->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">هیچ کاربری یافت نشد.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $users->links('custom.admin-pagination') }}
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأیید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این کاربر اطمینان دارید؟ این عمل قابل بازگشت نیست.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteUser">حذف</button>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });

            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
