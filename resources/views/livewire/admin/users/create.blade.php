<div>
    @section('page-title', 'افزودن کاربر جدید')
    
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">افزودن کاربر جدید</h6>
                </div>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">نام و نام خانوادگی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" wire:model="name">
                                @error('name') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="mobile" class="form-label">شماره موبایل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="mobile" wire:model="mobile" placeholder="09123456789">
                                @error('mobile') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">ایمیل</label>
                            <input type="email" class="form-control" id="email" wire:model="email">
                            @error('email') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="password" class="form-label">رمز عبور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" wire:model="password">
                                @error('password') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="password_confirmation" class="form-label">تکرار رمز عبور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password_confirmation" wire:model="password_confirmation">
                                @error('password_confirmation') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="role" class="form-label">نقش کاربر <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" wire:model="role">
                                    <option value="user">کاربر عادی</option>
                                    <option value="admin">مدیر</option>
                                </select>
                                @error('role') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" wire:model="is_active">
                                    <label class="form-check-label" for="is_active">کاربر فعال</label>
                                </div>
                                @error('is_active') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                            </a>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> ذخیره کاربر
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">راهنمای ثبت کاربر</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li class="mb-2">نام و نام خانوادگی الزامی است.</li>
                        <li class="mb-2">شماره موبایل باید با 09 شروع شده و 11 رقم باشد.</li>
                        <li class="mb-2">ایمیل اختیاری است اما در صورت وارد کردن باید معتبر باشد.</li>
                        <li class="mb-2">رمز عبور باید حداقل 8 کاراکتر باشد.</li>
                        <li class="mb-2">مدیران دسترسی کامل به پنل مدیریت دارند.</li>
                        <li>کاربران غیرفعال نمی‌توانند وارد سیستم شوند.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
