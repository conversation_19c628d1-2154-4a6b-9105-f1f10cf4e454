<div>
    @section('page-title', 'ویرایش کاربر')
    
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">ویرایش کاربر: {{ $user->name ?: $user->mobile }}</h6>
                </div>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">نام و نام خانوادگی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" wire:model="name">
                                @error('name') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="mobile" class="form-label">شماره موبایل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="mobile" wire:model="mobile" placeholder="09123456789">
                                @error('mobile') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">ایمیل</label>
                            <input type="email" class="form-control" id="email" wire:model="email">
                            @error('email') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="password" class="form-label">رمز عبور جدید</label>
                                <input type="password" class="form-control" id="password" wire:model="password">
                                <div class="small text-muted mt-1">در صورت عدم تغییر رمز عبور، این فیلد را خالی بگذارید.</div>
                                @error('password') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="password_confirmation" class="form-label">تکرار رمز عبور جدید</label>
                                <input type="password" class="form-control" id="password_confirmation" wire:model="password_confirmation">
                                @error('password_confirmation') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="role" class="form-label">نقش کاربر <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" wire:model="role">
                                    <option value="user">کاربر عادی</option>
                                    <option value="admin">مدیر</option>
                                </select>
                                @error('role') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" wire:model="is_active">
                                    <label class="form-check-label" for="is_active">کاربر فعال</label>
                                </div>
                                @error('is_active') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                            </a>
                            
                            <div>
                                <button type="button" class="btn btn-danger me-2" wire:click="confirmDelete">
                                    <i class="fas fa-trash me-1"></i> حذف کاربر
                                </button>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> ذخیره تغییرات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">اطلاعات کاربر</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">شناسه کاربر:</h6>
                        <p>{{ $user->id }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">تاریخ ثبت:</h6>
                        <p>{{ $user->created_at->format('Y/m/d H:i') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">آخرین بروزرسانی:</h6>
                        <p>{{ $user->updated_at->format('Y/m/d H:i') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">تعداد آگهی‌ها:</h6>
                        <p>{{ $user->ads->count() }} آگهی</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">وضعیت تأیید موبایل:</h6>
                        <p>
                            @if($user->mobile_verified_at)
                                <span class="badge bg-success">تأیید شده</span>
                                <br>
                                <small class="text-muted">{{ $user->mobile_verified_at->format('Y/m/d H:i') }}</small>
                            @else
                                <span class="badge bg-warning">تأیید نشده</span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأیید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این کاربر اطمینان دارید؟ این عمل قابل بازگشت نیست و تمام آگهی‌های این کاربر نیز حذف خواهند شد.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteUser">حذف</button>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            
            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });
            
            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
