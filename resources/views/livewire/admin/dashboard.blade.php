<div>
    @section('page-title', 'داشبورد مدیریت')
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-primary fw-bold text-uppercase mb-1">کل آگهی‌ها</h6>
                            <h2 class="mb-0 fw-bold">{{ $totalAds }}</h2>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn stat-icon text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-success fw-bold text-uppercase mb-1">کاربران</h6>
                            <h2 class="mb-0 fw-bold">{{ $totalUsers }}</h2>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users stat-icon text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-info fw-bold text-uppercase mb-1">دسته‌بندی‌ها</h6>
                            <h2 class="mb-0 fw-bold">{{ $totalCategories }}</h2>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list stat-icon text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-warning fw-bold text-uppercase mb-1">درآمد (تومان)</h6>
                            <h2 class="mb-0 fw-bold">{{ number_format($totalRevenue) }}</h2>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave stat-icon text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">آمار آگهی‌ها در {{ $currentYear }}</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="chartOptions" data-bs-toggle="dropdown" aria-expanded="false">
                            گزینه‌ها
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="chartOptions">
                            <li><a class="dropdown-item" href="#" wire:click="setChartPeriod('month')">ماهانه</a></li>
                            <li><a class="dropdown-item" href="#" wire:click="setChartPeriod('week')">هفتگی</a></li>
                            <li><a class="dropdown-item" href="#" wire:click="setChartPeriod('day')">روزانه</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="adsChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">توزیع آگهی‌ها بر اساس دسته‌بندی</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="categoryPieChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Latest Ads and Users -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">آخرین آگهی‌ها</h6>
                    <a href="{{ route('admin.ads.index') }}" class="btn btn-sm btn-primary">مشاهده همه</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>عنوان</th>
                                    <th>دسته‌بندی</th>
                                    <th>کاربر</th>
                                    <th>قیمت</th>
                                    <th>نوع</th>
                                    <th>تاریخ</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($latestAds as $ad)
                                    <tr>
                                        <td>
                                            <a href="{{ route('ads.show', $ad) }}" target="_blank">{{ Str::limit($ad->title, 30) }}</a>
                                        </td>
                                        <td>{{ $ad->category->name }}</td>
                                        <td>{{ $ad->user->name ?: $ad->user->mobile }}</td>
                                        <td>{{ number_format($ad->price) }}</td>
                                        <td>
                                            @if($ad->is_premium)
                                                <span class="badge bg-warning">ویژه</span>
                                            @elseif($ad->is_featured)
                                                <span class="badge bg-info">برجسته</span>
                                            @elseif($ad->type == 'ladder')
                                                <span class="badge bg-primary">نردبانی</span>
                                            @else
                                                <span class="badge bg-secondary">عادی</span>
                                            @endif
                                        </td>
                                        <td>{{ $ad->created_at->format('Y/m/d') }}</td>
                                        <td>
                                            <a href="{{ route('admin.ads.edit', $ad) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">آخرین کاربران</h6>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-primary">مشاهده همه</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>نام</th>
                                    <th>موبایل</th>
                                    <th>تاریخ</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($latestUsers as $user)
                                    <tr>
                                        <td>{{ $user->name ?: 'بدون نام' }}</td>
                                        <td>{{ $user->mobile }}</td>
                                        <td>{{ $user->created_at->format('Y/m/d') }}</td>
                                        <td>
                                            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">آخرین پرداخت‌ها</h6>
                    <a href="{{ route('admin.payments.index') }}" class="btn btn-sm btn-primary">مشاهده همه</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>کاربر</th>
                                    <th>مبلغ</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($latestPayments as $payment)
                                    <tr>
                                        <td>{{ $payment->user->name ?: $payment->user->mobile }}</td>
                                        <td>{{ number_format($payment->amount) }}</td>
                                        <td>
                                            @if($payment->status == 'completed')
                                                <span class="badge bg-success">موفق</span>
                                            @elseif($payment->status == 'pending')
                                                <span class="badge bg-warning">در انتظار</span>
                                            @else
                                                <span class="badge bg-danger">ناموفق</span>
                                            @endif
                                        </td>
                                        <td>{{ $payment->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('livewire:load', function() {
            // Line Chart for Ads
            const adsCtx = document.getElementById('adsChart').getContext('2d');
            const adsChart = new Chart(adsCtx, {
                type: 'line',
                data: {
                    labels: @json($chartLabels),
                    datasets: [
                        {
                            label: 'آگهی‌های جدید',
                            data: @json($chartData),
                            backgroundColor: 'rgba(78, 115, 223, 0.05)',
                            borderColor: 'rgba(78, 115, 223, 1)',
                            pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                            borderWidth: 2,
                            fill: true
                        }
                    ]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            
            // Pie Chart for Categories
            const categoryCtx = document.getElementById('categoryPieChart').getContext('2d');
            const categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: @json($categoryNames),
                    datasets: [{
                        data: @json($categoryData),
                        backgroundColor: [
                            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                            '#6f42c1', '#20c9a6', '#5a5c69', '#858796', '#5a5c69'
                        ],
                        hoverBackgroundColor: [
                            '#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#be2617',
                            '#5d36a4', '#169c82', '#4e4f58', '#717384', '#4e4f58'
                        ],
                        hoverBorderColor: "rgba(234, 236, 244, 1)",
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    },
                    cutout: '60%'
                }
            });
            
            // Update charts when data changes
            Livewire.on('chartDataUpdated', data => {
                adsChart.data.labels = data.labels;
                adsChart.data.datasets[0].data = data.values;
                adsChart.update();
            });
        });
    </script>
    @endpush
</div>
