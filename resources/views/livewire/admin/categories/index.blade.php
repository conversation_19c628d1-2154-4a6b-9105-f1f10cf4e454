<div>
    @section('page-title', 'مدیریت دسته‌بندی‌ها')
    
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold">لیست دسته‌بندی‌ها</h6>
            <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> افزودن دسته‌بندی جدید
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                </div>
                <div class="col-md-3">
                    <select wire:model.live="parentFilter" class="form-control">
                        <option value="">همه دسته‌بندی‌ها</option>
                        <option value="root">دسته‌بندی‌های اصلی</option>
                        @foreach($parentCategories as $category)
                            <option value="{{ $category->id }}">زیردسته‌های {{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <select wire:model.live="featuredFilter" class="form-control">
                        <option value="">همه</option>
                        <option value="1">ویژه</option>
                        <option value="0">غیر ویژه</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="perPage" class="form-control">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">ردیف</th>
                            <th>نام</th>
                            <th>اسلاگ</th>
                            <th>دسته والد</th>
                            <th>آیکون</th>
                            <th>ترتیب</th>
                            <th>ویژه</th>
                            <th>تعداد آگهی</th>
                            <th style="width: 150px;">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($categories as $category)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <a href="{{ route('category.show', $category->slug) }}" target="_blank">
                                        {{ $category->name }}
                                    </a>
                                </td>
                                <td>{{ $category->slug }}</td>
                                <td>
                                    @if($category->parent)
                                        <a href="{{ route('admin.categories.edit', $category->parent) }}">
                                            {{ $category->parent->name }}
                                        </a>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($category->icon)
                                        <i class="fas {{ $category->icon }}"></i>
                                        <small class="text-muted">{{ $category->icon }}</small>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>{{ $category->order }}</td>
                                <td>
                                    @if($category->is_featured)
                                        <span class="badge bg-success">بله</span>
                                    @else
                                        <span class="badge bg-secondary">خیر</span>
                                    @endif
                                </td>
                                <td>{{ $category->ads_count }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button wire:click="confirmDelete({{ $category->id }})" class="btn btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">هیچ دسته‌بندی یافت نشد.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="mt-3">
                {{ $categories->links() }}
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تایید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این دسته‌بندی اطمینان دارید؟
                    @if($categoryToDelete && $categoryToDelete->children->count() > 0)
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            این دسته‌بندی دارای {{ $categoryToDelete->children->count() }} زیردسته است که با حذف آن، تمامی زیردسته‌ها نیز حذف خواهند شد.
                        </div>
                    @endif
                    
                    @if($categoryToDelete && $categoryToDelete->ads->count() > 0)
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            این دسته‌بندی دارای {{ $categoryToDelete->ads->count() }} آگهی است. با حذف دسته‌بندی، آگهی‌های مرتبط بدون دسته‌بندی خواهند شد.
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteCategory">حذف</button>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            
            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });
            
            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
