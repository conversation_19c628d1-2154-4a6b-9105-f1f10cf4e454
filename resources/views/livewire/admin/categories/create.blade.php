<div>
    @section('page-title', 'افزودن دسته‌بندی جدید')
    
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">افزودن دسته‌بندی جدید</h6>
                </div>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">نام دسته‌بندی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" wire:model="name" wire:keyup="generateSlug">
                                @error('name') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="slug" class="form-label">اسلاگ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="slug" wire:model="slug">
                                @error('slug') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="parent_id" class="form-label">دسته والد</label>
                                <select class="form-control" id="parent_id" wire:model="parent_id">
                                    <option value="">بدون والد (دسته اصلی)</option>
                                    @foreach($parentCategories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                @error('parent_id') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="icon" class="form-label">آیکون</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas {{ $icon ?: 'fa-folder' }}"></i>
                                    </span>
                                    <input type="text" class="form-control" id="icon" wire:model="icon" placeholder="fa-folder">
                                </div>
                                <div class="small text-muted mt-1">
                                    نام کلاس آیکون از Font Awesome را وارد کنید. مثال: fa-home
                                    <a href="https://fontawesome.com/icons" target="_blank">مشاهده لیست آیکون‌ها</a>
                                </div>
                                @error('icon') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="order" class="form-label">ترتیب نمایش</label>
                                <input type="number" class="form-control" id="order" wire:model="order" min="0">
                                <div class="small text-muted mt-1">
                                    دسته‌بندی‌ها بر اساس این عدد به صورت صعودی مرتب می‌شوند.
                                </div>
                                @error('order') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label d-block">ویژگی‌ها</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_featured" wire:model="is_featured">
                                    <label class="form-check-label" for="is_featured">دسته‌بندی ویژه</label>
                                </div>
                                <div class="small text-muted mt-1">
                                    دسته‌بندی‌های ویژه در صفحه اصلی و بخش‌های ویژه نمایش داده می‌شوند.
                                </div>
                                @error('is_featured') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">توضیحات</label>
                            <textarea class="form-control" id="description" wire:model="description" rows="3"></textarea>
                            @error('description') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="image" class="form-label">تصویر دسته‌بندی</label>
                            <input type="file" class="form-control" id="image" wire:model="image" accept="image/*">
                            <div class="small text-muted mt-1">
                                فرمت‌های مجاز: JPG، PNG، GIF. حداکثر حجم: 2MB
                            </div>
                            @error('image') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            
                            @if($image)
                                <div class="mt-2">
                                    <img src="{{ $image->temporaryUrl() }}" alt="Preview" class="img-thumbnail" style="max-height: 200px;">
                                </div>
                            @endif
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">تنظیمات SEO</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="meta_title" class="form-label">عنوان متا</label>
                                    <input type="text" class="form-control" id="meta_title" wire:model="meta_title">
                                    @error('meta_title') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="meta_description" class="form-label">توضیحات متا</label>
                                    <textarea class="form-control" id="meta_description" wire:model="meta_description" rows="2"></textarea>
                                    @error('meta_description') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                                </div>
                                
                                <div class="mb-0">
                                    <label for="meta_keywords" class="form-label">کلمات کلیدی متا</label>
                                    <input type="text" class="form-control" id="meta_keywords" wire:model="meta_keywords">
                                    <div class="small text-muted mt-1">
                                        کلمات کلیدی را با کاما (،) از هم جدا کنید.
                                    </div>
                                    @error('meta_keywords') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                            </a>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> ذخیره دسته‌بندی
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">راهنما</h6>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">نکات مهم:</h6>
                    <ul class="small">
                        <li>نام دسته‌بندی باید منحصر به فرد باشد.</li>
                        <li>اسلاگ به صورت خودکار از روی نام ساخته می‌شود، اما می‌توانید آن را تغییر دهید.</li>
                        <li>اسلاگ باید شامل حروف انگلیسی، اعداد و خط تیره باشد.</li>
                        <li>برای آیکون می‌توانید از کلاس‌های Font Awesome استفاده کنید.</li>
                        <li>دسته‌بندی‌های ویژه در بخش‌های مخصوص سایت نمایش داده می‌شوند.</li>
                    </ul>
                    
                    <h6 class="fw-bold mt-3">ساختار دسته‌بندی:</h6>
                    <ul class="small">
                        <li>دسته‌بندی‌های اصلی: دسته‌بندی‌هایی که والد ندارند.</li>
                        <li>زیردسته‌ها: دسته‌بندی‌هایی که به یک دسته‌بندی اصلی متصل هستند.</li>
                        <li>توصیه می‌شود حداکثر از دو سطح دسته‌بندی استفاده کنید.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
