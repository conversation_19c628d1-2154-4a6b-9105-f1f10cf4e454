<div>
    @section('page-title', 'مدیریت پکیج‌ها')

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold">لیست پکیج‌ها</h6>
            <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> افزودن پکیج جدید
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                </div>
                <div class="col-md-3">
                    <select wire:model.live="statusFilter" class="form-control">
                        <option value="">همه وضعیت‌ها</option>
                        <option value="1">فعال</option>
                        <option value="0">غیرفعال</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="perPage" class="form-control">
                        <option value="10">10 مورد</option>
                        <option value="25">25 مورد</option>
                        <option value="50">50 مورد</option>
                        <option value="100">100 مورد</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">ردیف</th>
                            <th wire:click="sortBy('name')" style="cursor: pointer;">
                                نام پکیج
                                @if($sortField === 'name')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th wire:click="sortBy('price')" style="cursor: pointer;">
                                قیمت
                                @if($sortField === 'price')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th>مدت زمان</th>
                            <th>ویژگی‌ها</th>
                            <th>تعداد آگهی</th>
                            <th>وضعیت</th>
                            <th wire:click="sortBy('created_at')" style="cursor: pointer;">
                                تاریخ ایجاد
                                @if($sortField === 'created_at')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </th>
                            <th style="width: 150px;">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($packages as $package)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $package->name }}</td>
                                <td>{{ number_format($package->price) }} تومان</td>
                                <td>{{ $package->duration_days }} روز</td>
                                <td>
                                    @if($package->is_featured)
                                        <span class="badge bg-warning me-1">ویژه</span>
                                    @endif
                                    @if($package->is_premium)
                                        <span class="badge bg-info">پرمیوم</span>
                                    @endif
                                </td>
                                <td>{{ $package->ads_count }}</td>
                                <td>
                                    @if($package->is_active)
                                        <span class="badge bg-success">فعال</span>
                                    @else
                                        <span class="badge bg-secondary">غیرفعال</span>
                                    @endif
                                </td>
                                <td>{{ $package->created_at->format('Y/m/d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.packages.edit', $package) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <button type="button" class="btn btn-sm btn-{{ $package->is_active ? 'warning' : 'success' }}"
                                                wire:click="togglePackageStatus({{ $package->id }})">
                                            <i class="fas fa-{{ $package->is_active ? 'ban' : 'check' }}"></i>
                                        </button>

                                        <button type="button" class="btn btn-sm btn-danger"
                                                wire:click="confirmDelete({{ $package->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">هیچ پکیجی یافت نشد.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $packages->links('custom.admin-pagination') }}
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأیید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این پکیج اطمینان دارید؟ این عمل قابل بازگشت نیست.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deletePackage">حذف</button>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });

            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
