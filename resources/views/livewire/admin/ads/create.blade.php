<div>
    @section('page-title', 'افزودن آگهی جدید')
    
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">افزودن آگهی جدید</h6>
                </div>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="title" class="form-label">عنوان آگهی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" wire:model="title">
                                @error('title') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="price" class="form-label">قیمت <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" wire:model="price">
                                    <span class="input-group-text">تومان</span>
                                </div>
                                @error('price') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category_id" class="form-label">دسته‌بندی <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" wire:model="category_id">
                                    <option value="">انتخاب دسته‌بندی</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                @error('category_id') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="location" class="form-label">موقعیت <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="location" wire:model="location">
                                @error('location') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="package_id" class="form-label">پکیج</label>
                                <select class="form-select" id="package_id" wire:model="package_id">
                                    <option value="">بدون پکیج</option>
                                    @foreach($packages as $package)
                                        <option value="{{ $package->id }}">{{ $package->name }} - {{ number_format($package->price) }} تومان</option>
                                    @endforeach
                                </select>
                                @error('package_id') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="type" class="form-label">نوع آگهی</label>
                                <select class="form-select" id="type" wire:model="type">
                                    <option value="basic">معمولی</option>
                                    <option value="featured">ویژه</option>
                                    <option value="premium">پرمیوم</option>
                                    <option value="ladder">نردبانی</option>
                                </select>
                                @error('type') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_featured" wire:model="is_featured">
                                    <label class="form-check-label" for="is_featured">آگهی ویژه</label>
                                </div>
                                @error('is_featured') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_premium" wire:model="is_premium">
                                    <label class="form-check-label" for="is_premium">آگهی پرمیوم</label>
                                </div>
                                @error('is_premium') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="expires_at" class="form-label">تاریخ انقضا</label>
                            <input type="date" class="form-control" id="expires_at" wire:model="expires_at">
                            @error('expires_at') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">توضیحات <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" wire:model="description" rows="5"></textarea>
                            @error('description') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="images" class="form-label">تصاویر <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="images" wire:model="images" multiple accept="image/*">
                            <div class="small text-muted mt-1">
                                حداکثر 5 تصویر، هر تصویر حداکثر 2 مگابایت
                            </div>
                            @error('images') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            @error('images.*') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            
                            @if(count($images) > 0)
                                <div class="row mt-3">
                                    @foreach($images as $index => $image)
                                        <div class="col-md-3 mb-3">
                                            <div class="card h-100 {{ $primaryImage == $index ? 'border-primary' : '' }}">
                                                <img src="{{ $image->temporaryUrl() }}" class="card-img-top" alt="تصویر آگهی">
                                                <div class="card-body p-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="primaryImage" id="primaryImage{{ $index }}" value="{{ $index }}" wire:model="primaryImage">
                                                        <label class="form-check-label" for="primaryImage{{ $index }}">
                                                            تصویر اصلی
                                                        </label>
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-danger w-100 mt-2" wire:click="removeImage({{ $index }})">
                                                        <i class="fas fa-trash-alt"></i> حذف
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.ads.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                            </a>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> ذخیره آگهی
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">راهنمای ثبت آگهی</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li class="mb-2">عنوان آگهی باید مختصر و گویا باشد.</li>
                        <li class="mb-2">قیمت را به تومان وارد کنید.</li>
                        <li class="mb-2">حتماً دسته‌بندی مناسب را انتخاب کنید.</li>
                        <li class="mb-2">در صورت انتخاب پکیج، هزینه آن باید پرداخت شود.</li>
                        <li class="mb-2">آگهی‌های ویژه و پرمیوم در نتایج جستجو بالاتر نمایش داده می‌شوند.</li>
                        <li class="mb-2">آگهی‌های نردبانی به صورت دوره‌ای به بالای لیست منتقل می‌شوند.</li>
                        <li>حداقل یک تصویر برای آگهی الزامی است.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
