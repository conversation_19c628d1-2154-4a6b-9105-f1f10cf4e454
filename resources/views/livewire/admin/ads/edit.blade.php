<div>
    @section('page-title', 'ویرایش آگهی')
    
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 fw-bold">ویرایش آگهی: {{ $ad->title }}</h6>
                </div>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="title" class="form-label">عنوان آگهی <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" wire:model="title">
                                @error('title') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="category_id" class="form-label">دسته‌بندی <span class="text-danger">*</span></label>
                                <select class="form-control" id="category_id" wire:model="category_id">
                                    <option value="">انتخاب دسته‌بندی</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                @error('category_id') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">قیمت (تومان) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" wire:model="price">
                                @error('price') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="location" class="form-label">موقعیت <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="location" wire:model="location">
                                @error('location') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="package_id" class="form-label">پکیج</label>
                                <select class="form-control" id="package_id" wire:model="package_id">
                                    <option value="">بدون پکیج (رایگان)</option>
                                    @foreach($packages as $package)
                                        <option value="{{ $package->id }}">{{ $package->name }} - {{ number_format($package->price) }} تومان</option>
                                    @endforeach
                                </select>
                                @error('package_id') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="type" class="form-label">نوع آگهی</label>
                                <select class="form-control" id="type" wire:model="type">
                                    <option value="basic">عادی</option>
                                    <option value="featured">برجسته</option>
                                    <option value="premium">ویژه</option>
                                    <option value="ladder">نردبانی</option>
                                </select>
                                @error('type') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="expires_at" class="form-label">تاریخ انقضا</label>
                                <input type="date" class="form-control" id="expires_at" wire:model="expires_at">
                                <div class="small text-muted mt-1">
                                    اگر خالی باشد، آگهی بدون تاریخ انقضا خواهد بود.
                                </div>
                                @error('expires_at') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label d-block">ویژگی‌ها</label>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_featured" wire:model="is_featured">
                                    <label class="form-check-label" for="is_featured">آگهی برجسته</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_premium" wire:model="is_premium">
                                    <label class="form-check-label" for="is_premium">آگهی ویژه</label>
                                </div>
                                @error('is_featured') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                                @error('is_premium') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">توضیحات <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" wire:model="description" rows="5"></textarea>
                            @error('description') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.ads.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                            </a>
                            
                            <div>
                                <button type="button" class="btn btn-danger me-2" wire:click="confirmDelete">
                                    <i class="fas fa-trash me-1"></i> حذف آگهی
                                </button>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> ذخیره تغییرات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold">اطلاعات آگهی</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">شناسه آگهی:</h6>
                        <p>{{ $ad->id }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">کاربر:</h6>
                        <p>
                            <a href="{{ route('admin.users.edit', $ad->user) }}">
                                {{ $ad->user->name ?: $ad->user->mobile }}
                            </a>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">تاریخ ثبت:</h6>
                        <p>{{ $ad->created_at->format('Y/m/d H:i') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">آخرین بروزرسانی:</h6>
                        <p>{{ $ad->updated_at->format('Y/m/d H:i') }}</p>
                    </div>
                    
                    <div class="mb-0">
                        <h6 class="fw-bold">لینک آگهی:</h6>
                        <a href="{{ route('ads.show', $ad) }}" target="_blank">
                            {{ route('ads.show', $ad) }}
                        </a>
                    </div>
                </div>
            </div>
            
            @if($ad->payments->count() > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold">پرداخت‌های آگهی</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>مبلغ</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ad->payments as $payment)
                                        <tr>
                                            <td>{{ number_format($payment->amount) }}</td>
                                            <td>
                                                @if($payment->status == 'completed')
                                                    <span class="badge bg-success">موفق</span>
                                                @elseif($payment->status == 'pending')
                                                    <span class="badge bg-warning">در انتظار</span>
                                                @else
                                                    <span class="badge bg-danger">ناموفق</span>
                                                @endif
                                            </td>
                                            <td>{{ $payment->created_at->format('Y/m/d') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تایید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این آگهی اطمینان دارید؟
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        این عملیات غیرقابل بازگشت است.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteAd">حذف</button>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            
            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });
            
            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
