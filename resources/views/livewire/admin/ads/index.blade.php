<div>
    @section('page-title', 'مدیریت آگهی‌ها')

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold">لیست آگهی‌ها</h6>
            <a href="{{ route('admin.ads.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> افزودن آگهی جدید
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3">
                    <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                </div>
                <div class="col-md-2">
                    <select wire:model.live="categoryFilter" class="form-control">
                        <option value="">همه دسته‌بندی‌ها</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="typeFilter" class="form-control">
                        <option value="">همه انواع</option>
                        <option value="basic">عادی</option>
                        <option value="featured">برجسته</option>
                        <option value="premium">ویژه</option>
                        <option value="ladder">نردبانی</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="statusFilter" class="form-control">
                        <option value="">همه وضعیت‌ها</option>
                        <option value="active">فعال</option>
                        <option value="expired">منقضی شده</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="sortField" class="form-control">
                        <option value="created_at">تاریخ ثبت</option>
                        <option value="price">قیمت</option>
                        <option value="title">عنوان</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <select wire:model.live="perPage" class="form-control">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">ردیف</th>
                            <th>عنوان</th>
                            <th>دسته‌بندی</th>
                            <th>کاربر</th>
                            <th>قیمت</th>
                            <th>نوع</th>
                            <th>وضعیت</th>
                            <th>تاریخ ثبت</th>
                            <th>تاریخ انقضا</th>
                            <th style="width: 150px;">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($ads as $ad)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    <a href="{{ route('ads.show', $ad) }}" target="_blank">
                                        {{ Str::limit($ad->title, 30) }}
                                    </a>
                                </td>
                                <td>{{ $ad->category->name }}</td>
                                <td>
                                    <a href="{{ route('admin.users.edit', $ad->user) }}">
                                        {{ $ad->user->name ?: $ad->user->mobile }}
                                    </a>
                                </td>
                                <td>{{ number_format($ad->price) }}</td>
                                <td>
                                    @if($ad->is_premium)
                                        <span class="badge bg-warning">ویژه</span>
                                    @elseif($ad->is_featured)
                                        <span class="badge bg-info">برجسته</span>
                                    @elseif($ad->type == 'ladder')
                                        <span class="badge bg-primary">نردبانی</span>
                                    @else
                                        <span class="badge bg-secondary">عادی</span>
                                    @endif
                                </td>
                                <td>
                                    @if($ad->expires_at && $ad->expires_at->isPast())
                                        <span class="badge bg-danger">منقضی شده</span>
                                    @else
                                        <span class="badge bg-success">فعال</span>
                                    @endif
                                </td>
                                <td>{{ $ad->created_at->format('Y/m/d') }}</td>
                                <td>{{ $ad->expires_at ? $ad->expires_at->format('Y/m/d') : 'نامحدود' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('ads.show', $ad) }}" target="_blank" class="btn btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.ads.edit', $ad) }}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button wire:click="confirmDelete({{ $ad->id }})" class="btn btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">هیچ آگهی یافت نشد.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $ads->links('custom.admin-pagination') }}
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تایید حذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    آیا از حذف این آگهی اطمینان دارید؟
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        این عملیات غیرقابل بازگشت است.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteAd">حذف</button>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('livewire:load', function() {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

            Livewire.on('showDeleteModal', () => {
                deleteModal.show();
            });

            Livewire.on('hideDeleteModal', () => {
                deleteModal.hide();
            });
        });
    </script>
    @endpush
</div>
