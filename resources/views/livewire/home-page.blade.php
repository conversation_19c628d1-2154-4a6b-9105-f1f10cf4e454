<div>
    <!-- Hero Section -->
    <div class="hero-section mb-5 animate__animated animate__fadeInDown">
        <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
            <div class="card-body py-5">
                <h1 class="display-4 fw-bold mb-3 animate__animated animate__pulse animate__infinite">
                    <i class="fas fa-bullhorn me-3"></i>
                    بیست رنگ
                </h1>
                <p class="lead mb-4">بزرگترین سایت آگهی رایگان ایران</p>
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="input-group input-group-lg">
                            <input type="text" class="form-control" placeholder="دنبال چی می‌گردی؟">
                            <button class="btn btn-light text-primary fw-bold" type="button">
                                <i class="fas fa-search me-1"></i>
                                جستجو
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row mt-4 text-center">
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold">{{ number_format(100) }}</h3>
                            <p class="mb-0">آگهی فعال</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold">{{ number_format(15) }}</h3>
                            <p class="mb-0">کاربر عضو</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold">{{ $categories->count() }}</h3>
                            <p class="mb-0">دسته‌بندی</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold">24/7</h3>
                            <p class="mb-0">پشتیبانی</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="mb-5 animate__animated animate__fadeInUp">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-tags me-2 text-primary"></i>
                    دسته‌بندی‌ها
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($categories as $category)
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{{ route('ads.index', ['categoryFilter' => $category->id]) }}" class="text-decoration-none">
                                <div class="card h-100 text-center category-card">
                                    <div class="card-body">
                                        <i class="fas fa-folder-open fa-2x text-primary mb-3"></i>
                                        <h6 class="card-title">{{ $category->name }}</h6>
                                        <p class="text-muted small">{{ $category->ads_count }} آگهی</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Ads Section -->
    @if($featuredAds->count() > 0)
    <div class="mb-5 animate__animated animate__fadeInUp">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">
                    <i class="fas fa-star me-2 text-warning"></i>
                    آگهی‌های ویژه
                </h3>
                <a href="{{ route('ads.index') }}" class="btn btn-outline-primary btn-sm">
                    مشاهده همه
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($featuredAds as $ad)
                        <div class="col-md-4 mb-4 animate__animated animate__fadeInUp" style="animation-delay: {{ $loop->index * 0.1 }}s">
                            <div class="card h-100 featured-ad-card">
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>ویژه
                                    </span>
                                </div>

                                <div class="card-header border-0 bg-transparent">
                                    <h6 class="mb-0">
                                        <a href="{{ route('ads.show', $ad) }}" class="text-decoration-none text-dark fw-bold">
                                            {{ \Illuminate\Support\Str::limit($ad->title, 40) }}
                                        </a>
                                    </h6>
                                </div>

                                <div class="card-body pt-0">
                                    <p class="text-muted small mb-3">{{ \Illuminate\Support\Str::limit($ad->description, 60) }}</p>

                                    <div class="mb-2">
                                        <i class="fas fa-tag text-success me-1"></i>
                                        <strong class="text-success">{{ number_format($ad->price) }} تومان</strong>
                                    </div>

                                    <div class="mb-2">
                                        <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                        <small class="text-muted">{{ $ad->location }}</small>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent border-0">
                                    <a href="{{ route('ads.show', $ad) }}" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-eye me-1"></i>
                                        مشاهده جزئیات
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Latest Ads Section -->
    <div class="mb-5 animate__animated animate__fadeInUp">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>
                    جدیدترین آگهی‌ها
                </h3>
                <a href="{{ route('ads.index') }}" class="btn btn-outline-primary btn-sm">
                    مشاهده همه
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($latestAds as $ad)
                        <div class="col-md-3 col-6 mb-3 animate__animated animate__fadeInUp" style="animation-delay: {{ $loop->index * 0.05 }}s">
                            <div class="card h-100 latest-ad-card">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <a href="{{ route('ads.show', $ad) }}" class="text-decoration-none text-dark">
                                            {{ \Illuminate\Support\Str::limit($ad->title, 30) }}
                                        </a>
                                    </h6>

                                    <div class="mb-2">
                                        <small class="text-success fw-bold">{{ number_format($ad->price) }} تومان</small>
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ $ad->location }}
                                        </small>
                                    </div>

                                    <div>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $ad->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mb-5 animate__animated animate__fadeInUp">
        <div class="card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none;">
            <div class="card-body py-5">
                <h2 class="fw-bold mb-3">آماده ثبت آگهی هستید؟</h2>
                <p class="lead mb-4">آگهی خود را رایگان ثبت کنید و به میلیون‌ها کاربر برسانید</p>
                @auth
                    <a href="{{ route('ads.create') }}" class="btn btn-light btn-lg text-primary fw-bold">
                        <i class="fas fa-plus me-2"></i>
                        ثبت آگهی رایگان
                    </a>
                @else
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg text-primary fw-bold me-3">
                        <i class="fas fa-user-plus me-2"></i>
                        ثبت نام
                    </a>
                    <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        ورود
                    </a>
                @endauth
            </div>
        </div>
    </div>
</div>
