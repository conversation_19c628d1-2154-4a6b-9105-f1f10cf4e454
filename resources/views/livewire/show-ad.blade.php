<div>
    <div class="container py-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">خانه</a></li>
                <li class="breadcrumb-item"><a href="{{ route('category.show', $ad->category->slug) }}">{{ $ad->category->name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ $ad->title }}</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Ad Details -->
            <div class="col-lg-8">
                <!-- Ad Title and Badge -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h1 class="h2 mb-0">{{ $ad->title }}</h1>
                    <div>
                        @if($ad->is_premium)
                            <span class="badge bg-warning">ویژه</span>
                        @elseif($ad->is_featured)
                            <span class="badge bg-info">برجسته</span>
                        @elseif($ad->type == 'ladder')
                            <span class="badge bg-primary">نردبانی</span>
                        @endif
                    </div>
                </div>

                <!-- Ad Images Gallery -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-body p-0">
                        @if($ad->images->count() > 0)
                            <div id="adCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-indicators">
                                    @foreach($ad->images as $index => $image)
                                        <button type="button" data-bs-target="#adCarousel" data-bs-slide-to="{{ $index }}" class="{{ $index === 0 ? 'active' : '' }}" aria-current="{{ $index === 0 ? 'true' : 'false' }}" aria-label="Slide {{ $index + 1 }}"></button>
                                    @endforeach
                                </div>
                                <div class="carousel-inner">
                                    @foreach($ad->images as $index => $image)
                                        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                            <img src="{{ asset('storage/' . $image->image_path) }}" class="d-block w-100" alt="{{ $ad->title }}" style="height: 400px; object-fit: contain;">
                                        </div>
                                    @endforeach
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#adCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">قبلی</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#adCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">بعدی</span>
                                </button>
                            </div>

                            <!-- Thumbnails -->
                            @if($ad->images->count() > 1)
                                <div class="d-flex mt-2 overflow-auto">
                                    @foreach($ad->images as $index => $image)
                                        <div class="me-2" style="width: 80px; height: 60px;">
                                            <img src="{{ asset('storage/' . $image->image_path) }}" class="img-thumbnail" alt="{{ $ad->title }}" style="width: 100%; height: 100%; object-fit: cover; cursor: pointer;" data-bs-target="#adCarousel" data-bs-slide-to="{{ $index }}">
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        @else
                            <img src="{{ asset('images/no-image.jpg') }}" class="img-fluid w-100" alt="{{ $ad->title }}" style="height: 400px; object-fit: contain;">
                        @endif
                    </div>
                </div>

                <!-- Ad Details Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">جزئیات آگهی</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-tag text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">قیمت</small>
                                        <strong>{{ number_format($ad->price) }} تومان</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-map-marker-alt text-danger"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">موقعیت</small>
                                        <strong>{{ $ad->location }}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-list text-info"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">دسته‌بندی</small>
                                        <strong>{{ $ad->category->name }}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-calendar-alt text-success"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">تاریخ ثبت</small>
                                        <strong>{{ $ad->created_at->format('Y/m/d') }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5 class="border-bottom pb-2 mb-3">توضیحات</h5>
                        <div class="ad-description mb-4">
                            {!! nl2br(e($ad->description)) !!}
                        </div>

                        <!-- Share Buttons -->
                        <div class="d-flex justify-content-between align-items-center border-top pt-3">
                            <div>
                                <span class="text-muted">اشتراک‌گذاری:</span>
                                <a href="https://telegram.me/share/url?url={{ urlencode(route('ads.show', $ad)) }}&text={{ urlencode($ad->title) }}" class="btn btn-sm btn-outline-info ms-2" target="_blank">
                                    <i class="fab fa-telegram"></i>
                                </a>
                                <a href="https://wa.me/?text={{ urlencode($ad->title . ' - ' . route('ads.show', $ad)) }}" class="btn btn-sm btn-outline-success ms-2" target="_blank">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <a href="https://twitter.com/intent/tweet?text={{ urlencode($ad->title) }}&url={{ urlencode(route('ads.show', $ad)) }}" class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-danger" wire:click="reportAd">
                                    <i class="fas fa-flag me-1"></i> گزارش آگهی
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Owner Actions -->
                @if(auth()->check() && (auth()->id() === $ad->user_id || auth()->user()->hasRole('admin')))
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">مدیریت آگهی</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex">
                            <a href="{{ route('ads.edit', $ad) }}" class="btn btn-primary me-2">
                                <i class="fas fa-edit me-1"></i> ویرایش آگهی
                            </a>
                            <button wire:click="delete" class="btn btn-danger me-2" onclick="return confirm('آیا از حذف این آگهی اطمینان دارید؟')">
                                <i class="fas fa-trash me-1"></i> حذف آگهی
                            </button>
                            @if($ad->expires_at && $ad->expires_at->isPast())
                                <button wire:click="renewAd" class="btn btn-success">
                                    <i class="fas fa-sync-alt me-1"></i> تمدید آگهی
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <!-- Similar Ads -->
                @if($similarAds->count() > 0)
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">آگهی‌های مشابه</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($similarAds as $similarAd)
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <img src="{{ $similarAd->imageUrl }}" class="card-img-top" alt="{{ $similarAd->title }}" style="height: 150px; object-fit: cover;">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ Str::limit($similarAd->title, 40) }}</h6>
                                            <p class="card-text text-primary fw-bold">{{ number_format($similarAd->price) }} تومان</p>
                                        </div>
                                        <div class="card-footer bg-white border-top-0">
                                            <a href="{{ route('ads.show', $similarAd) }}" class="btn btn-sm btn-outline-primary w-100">مشاهده آگهی</a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Info -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">اطلاعات تماس</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            @if($ad->user->avatar)
                                <img src="{{ asset('storage/' . $ad->user->avatar) }}" class="rounded-circle mb-2" width="80" height="80" style="object-fit: cover;">
                            @else
                                <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 80px; height: 80px;">
                                    <i class="fas fa-user fa-2x text-secondary"></i>
                                </div>
                            @endif
                            <h5>{{ $ad->user->name ?: 'کاربر' }}</h5>
                            <p class="text-muted small">عضو از {{ $ad->user->created_at->format('Y/m/d') }}</p>
                        </div>

                        @if(auth()->check())
                            <div class="d-grid gap-2">
                                <a href="tel:{{ $ad->user->mobile }}" class="btn btn-primary">
                                    <i class="fas fa-phone-alt me-2"></i> {{ $ad->user->mobile }}
                                </a>
                                <button class="btn btn-outline-success" wire:click="showContactForm">
                                    <i class="fas fa-envelope me-2"></i> ارسال پیام
                                </button>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                برای مشاهده اطلاعات تماس، لطفا <a href="{{ route('login') }}">وارد</a> شوید.
                            </div>
                        @endif

                        <!-- User's Other Ads -->
                        @if($userOtherAds->count() > 0)
                            <div class="mt-4">
                                <h6 class="border-bottom pb-2">سایر آگهی‌های این کاربر</h6>
                                <ul class="list-group list-group-flush">
                                    @foreach($userOtherAds as $otherAd)
                                        <li class="list-group-item px-0">
                                            <a href="{{ route('ads.show', $otherAd) }}" class="text-decoration-none">
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $otherAd->imageUrl }}" class="rounded me-2" width="50" height="50" style="object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-0 text-truncate" style="max-width: 200px;">{{ $otherAd->title }}</h6>
                                                        <small class="text-primary">{{ number_format($otherAd->price) }} تومان</small>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                                <div class="text-center mt-2">
                                    <a href="{{ route('ads.index', ['user_id' => $ad->user_id]) }}" class="btn btn-sm btn-outline-secondary w-100">مشاهده همه آگهی‌ها</a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Safety Tips -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">نکات ایمنی</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="d-flex align-items-center mb-3">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <span>قبل از خرید، کالا را بررسی کنید.</span>
                            </li>
                            <li class="d-flex align-items-center mb-3">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <span>در مکان‌های عمومی قرار ملاقات بگذارید.</span>
                            </li>
                            <li class="d-flex align-items-center mb-3">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <span>از پرداخت قبل از دریافت کالا خودداری کنید.</span>
                            </li>
                            <li class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <span>از صحت و سلامت کالا اطمینان حاصل کنید.</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Form Modal -->
    @if($showContactModal)
    <div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ارسال پیام به آگهی‌دهنده</h5>
                    <button type="button" class="btn-close" wire:click="hideContactForm"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="sendMessage">
                        <div class="mb-3">
                            <label for="message" class="form-label">پیام شما</label>
                            <textarea class="form-control" id="message" wire:model="message" rows="4" required></textarea>
                            @error('message') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" wire:click="hideContactForm">انصراف</button>
                            <button type="submit" class="btn btn-primary">ارسال پیام</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    <!-- Report Modal -->
    @if($showReportModal)
    <div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">گزارش آگهی</h5>
                    <button type="button" class="btn-close" wire:click="hideReportForm"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="submitReport">
                        <div class="mb-3">
                            <label class="form-label">دلیل گزارش</label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="reportReason" id="reason1" value="fake" wire:model="reportReason">
                                <label class="form-check-label" for="reason1">آگهی جعلی است</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="reportReason" id="reason2" value="offensive" wire:model="reportReason">
                                <label class="form-check-label" for="reason2">محتوای نامناسب دارد</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="reportReason" id="reason3" value="sold" wire:model="reportReason">
                                <label class="form-check-label" for="reason3">کالا فروخته شده است</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="reportReason" id="reason4" value="other" wire:model="reportReason">
                                <label class="form-check-label" for="reason4">سایر دلایل</label>
                            </div>
                            @error('reportReason') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>

                        @if($reportReason === 'other')
                        <div class="mb-3">
                            <label for="reportDescription" class="form-label">توضیحات</label>
                            <textarea class="form-control" id="reportDescription" wire:model="reportDescription" rows="3" required></textarea>
                            @error('reportDescription') <div class="text-danger small mt-1">{{ $message }}</div> @enderror
                        </div>
                        @endif

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" wire:click="hideReportForm">انصراف</button>
                            <button type="submit" class="btn btn-danger">ارسال گزارش</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif
</div>
