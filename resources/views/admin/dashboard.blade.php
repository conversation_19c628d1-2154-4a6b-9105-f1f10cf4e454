@extends('layouts.app')

@section('app-content')
<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5>منوی مدیریت</h5>
                </div>
                <div class="card-body">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">مدیریت دسته‌بندی‌ها</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">مدیریت آگهی‌ها</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">مدیریت کاربران</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">مدیریت پکیج‌ها</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">مدیریت پرداخت‌ها</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5>داشبورد مدیریت</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">تعداد آگهی‌ها</h5>
                                    <p class="card-text display-4">{{ \App\Models\Ad::count() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">تعداد کاربران</h5>
                                    <p class="card-text display-4">{{ \App\Models\User::count() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">تعداد پرداخت‌ها</h5>
                                    <p class="card-text display-4">{{ \App\Models\Payment::count() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>آخرین آگهی‌ها</h5>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>عنوان</th>
                                        <th>دسته‌بندی</th>
                                        <th>کاربر</th>
                                        <th>قیمت</th>
                                        <th>تاریخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(\App\Models\Ad::with(['category', 'user'])->latest()->take(5)->get() as $ad)
                                    <tr>
                                        <td>{{ $ad->title }}</td>
                                        <td>{{ $ad->category->name }}</td>
                                        <td>{{ $ad->user->name ?? $ad->user->mobile }}</td>
                                        <td>{{ number_format($ad->price) }}</td>
                                        <td>{{ $ad->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
