@if ($paginator->hasPages())
    <div class="admin-pagination-wrapper">
        <!-- Pagination Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="pagination-info">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    نمایش {{ $paginator->firstItem() }} تا {{ $paginator->lastItem() }} از {{ $paginator->total() }} رکورد
                </small>
            </div>
            <div class="pagination-summary">
                <small class="text-muted">
                    صفحه {{ $paginator->currentPage() }} از {{ $paginator->lastPage() }}
                </small>
            </div>
        </div>

        <!-- Pagination Navigation -->
        <nav aria-label="صفحه‌بندی" class="d-flex justify-content-center">
            <ul class="pagination pagination-sm mb-0">
                {{-- First Page Link --}}
                @if ($paginator->currentPage() > 3)
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->url(1) }}" title="صفحه اول">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                @endif

                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-right"></i>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->previousPageUrl() }}" title="صفحه قبل">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled">
                            <span class="page-link">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link">
                                        {{ $page }}
                                    </span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->nextPageUrl() }}" title="صفحه بعد">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-left"></i>
                        </span>
                    </li>
                @endif

                {{-- Last Page Link --}}
                @if ($paginator->currentPage() < $paginator->lastPage() - 2)
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}" title="صفحه آخر">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                @endif
            </ul>
        </nav>

        <!-- Quick Jump for large datasets -->
        @if ($paginator->lastPage() > 10)
            <div class="pagination-jump mt-3 text-center">
                <div class="d-inline-flex align-items-center">
                    <label class="form-label me-2 mb-0 small text-muted">پرش به:</label>
                    <input type="number" 
                           class="form-control form-control-sm" 
                           min="1" 
                           max="{{ $paginator->lastPage() }}" 
                           value="{{ $paginator->currentPage() }}"
                           style="width: 70px;"
                           onchange="window.location.href = '{{ $paginator->url(1) }}'.replace('page=1', 'page=' + this.value)">
                    <span class="text-muted ms-2 small">از {{ $paginator->lastPage() }}</span>
                </div>
            </div>
        @endif
    </div>

    <style>
        .admin-pagination-wrapper {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
            margin-top: 1rem;
        }

        .admin-pagination-wrapper .pagination {
            --bs-pagination-padding-x: 0.5rem;
            --bs-pagination-padding-y: 0.375rem;
            --bs-pagination-font-size: 0.875rem;
            --bs-pagination-border-radius: 0.25rem;
        }

        .admin-pagination-wrapper .page-link {
            border-radius: 0.25rem;
            margin: 0 1px;
            transition: all 0.15s ease-in-out;
        }

        .admin-pagination-wrapper .page-link:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-pagination-wrapper .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.25);
        }

        .pagination-info, .pagination-summary {
            font-size: 0.875rem;
        }

        @media (max-width: 576px) {
            .admin-pagination-wrapper {
                padding: 0.75rem;
            }
            
            .admin-pagination-wrapper .pagination {
                --bs-pagination-padding-x: 0.375rem;
                --bs-pagination-padding-y: 0.25rem;
                --bs-pagination-font-size: 0.75rem;
            }
            
            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
        }
    </style>
@endif
