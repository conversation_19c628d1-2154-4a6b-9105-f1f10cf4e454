@if ($paginator->hasPages())
    <div class="pagination-wrapper">
        <!-- Pagination Info -->
        <div class="pagination-info mb-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        نمایش {{ $paginator->firstItem() }} تا {{ $paginator->lastItem() }} از {{ $paginator->total() }} نتیجه
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        صفحه {{ $paginator->currentPage() }} از {{ $paginator->lastPage() }}
                    </small>
                </div>
            </div>
        </div>

        <!-- Pagination Navigation -->
        <nav aria-label="صفحه‌بندی">
            <ul class="pagination pagination-custom justify-content-center mb-0">
                {{-- First Page Link --}}
                @if ($paginator->currentPage() > 3)
                    <li class="page-item">
                        <button class="page-link" wire:click="gotoPage(1)" aria-label="صفحه اول" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <i class="fas fa-angle-double-right"></i>
                                <span class="d-none d-sm-inline ms-1">اول</span>
                            </span>
                            <span wire:loading>
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </li>
                @endif

                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-right"></i>
                            <span class="d-none d-sm-inline ms-1">قبلی</span>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <button class="page-link" wire:click="previousPage" aria-label="صفحه قبل" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <i class="fas fa-angle-right"></i>
                                <span class="d-none d-sm-inline ms-1">قبلی</span>
                            </span>
                            <span wire:loading>
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled d-none d-sm-block">
                            <span class="page-link">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link">
                                        {{ $page }}
                                        <span class="sr-only">(صفحه فعلی)</span>
                                    </span>
                                </li>
                            @else
                                <li class="page-item">
                                    <button class="page-link" wire:click="gotoPage({{ $page }})" wire:loading.attr="disabled">
                                        <span wire:loading.remove>{{ $page }}</span>
                                        <span wire:loading><i class="fas fa-spinner fa-spin"></i></span>
                                    </button>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <button class="page-link" wire:click="nextPage" aria-label="صفحه بعد" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <span class="d-none d-sm-inline me-1">بعدی</span>
                                <i class="fas fa-angle-left"></i>
                            </span>
                            <span wire:loading>
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <span class="d-none d-sm-inline me-1">بعدی</span>
                            <i class="fas fa-angle-left"></i>
                        </span>
                    </li>
                @endif

                {{-- Last Page Link --}}
                @if ($paginator->currentPage() < $paginator->lastPage() - 2)
                    <li class="page-item">
                        <button class="page-link" wire:click="gotoPage({{ $paginator->lastPage() }})" aria-label="صفحه آخر" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <span class="d-none d-sm-inline me-1">آخر</span>
                                <i class="fas fa-angle-double-left"></i>
                            </span>
                            <span wire:loading>
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </li>
                @endif
            </ul>
        </nav>

        <!-- Quick Jump -->
        @if ($paginator->lastPage() > 10)
            <div class="pagination-jump mt-3 text-center" x-data="{ jumpPage: {{ $paginator->currentPage() }} }">
                <div class="d-inline-flex align-items-center">
                    <label class="form-label me-2 mb-0 text-muted">پرش به صفحه:</label>
                    <input type="number"
                           class="form-control form-control-sm pagination-jump-input"
                           min="1"
                           max="{{ $paginator->lastPage() }}"
                           x-model="jumpPage"
                           style="width: 80px;"
                           @keydown.enter="$wire.gotoPage(jumpPage)">
                    <span class="text-muted ms-2">از {{ $paginator->lastPage() }}</span>
                    <button class="btn btn-sm btn-outline-primary ms-2"
                            @click="$wire.gotoPage(jumpPage)">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        @endif
    </div>

    <style>
        .pagination-custom {
            --bs-pagination-padding-x: 0.75rem;
            --bs-pagination-padding-y: 0.5rem;
            --bs-pagination-font-size: 0.875rem;
            --bs-pagination-color: #6c757d;
            --bs-pagination-bg: #fff;
            --bs-pagination-border-width: 1px;
            --bs-pagination-border-color: #dee2e6;
            --bs-pagination-border-radius: 0.375rem;
            --bs-pagination-hover-color: #0d6efd;
            --bs-pagination-hover-bg: #e9ecef;
            --bs-pagination-hover-border-color: #dee2e6;
            --bs-pagination-focus-color: #0d6efd;
            --bs-pagination-focus-bg: #e9ecef;
            --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            --bs-pagination-active-color: #fff;
            --bs-pagination-active-bg: #0d6efd;
            --bs-pagination-active-border-color: #0d6efd;
            --bs-pagination-disabled-color: #6c757d;
            --bs-pagination-disabled-bg: #fff;
            --bs-pagination-disabled-border-color: #dee2e6;
        }

        .pagination-custom .page-link {
            position: relative;
            display: block;
            padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
            font-size: var(--bs-pagination-font-size);
            color: var(--bs-pagination-color);
            text-decoration: none;
            background-color: var(--bs-pagination-bg);
            border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            border-radius: var(--bs-pagination-border-radius);
            margin: 0 2px;
            min-width: 40px;
            text-align: center;
            cursor: pointer;
        }

        .pagination-custom button.page-link {
            border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
            background-color: var(--bs-pagination-bg);
            color: var(--bs-pagination-color);
        }

        .pagination-custom button.page-link:focus {
            box-shadow: var(--bs-pagination-focus-box-shadow);
            outline: none;
        }

        .pagination-custom button.page-link:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }

        .pagination-custom .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .pagination-custom .page-link:hover {
            z-index: 2;
            color: var(--bs-pagination-hover-color);
            background-color: var(--bs-pagination-hover-bg);
            border-color: var(--bs-pagination-hover-border-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination-custom .page-item.active .page-link {
            z-index: 3;
            color: var(--bs-pagination-active-color);
            background-color: var(--bs-pagination-active-bg);
            border-color: var(--bs-pagination-active-border-color);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
        }

        .pagination-custom .page-item.disabled .page-link {
            color: var(--bs-pagination-disabled-color);
            pointer-events: none;
            background-color: var(--bs-pagination-disabled-bg);
            border-color: var(--bs-pagination-disabled-border-color);
        }

        .pagination-jump-input {
            text-align: center;
        }

        .pagination-wrapper {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
        }

        @media (max-width: 576px) {
            .pagination-custom .page-link {
                padding: 0.375rem 0.5rem;
                font-size: 0.75rem;
                min-width: 35px;
                margin: 0 1px;
            }

            .pagination-wrapper {
                padding: 1rem;
            }
        }
    </style>
@endif
