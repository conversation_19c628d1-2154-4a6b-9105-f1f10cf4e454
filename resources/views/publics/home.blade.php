@extends('layouts.public')

@section('public-content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="text-center">به سایت آگهی خوش آمدید</h1>
            <p class="text-center">سایت جامع آگهی با امکانات پیشرفته</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3>دسته‌بندی‌های آگهی</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(\App\Models\Category::all() as $category)
                            <div class="col-md-3 mb-3">
                                <a href="{{ route('ads.index', ['categoryFilter' => $category->id]) }}" class="btn btn-outline-primary w-100">
                                    {{ $category->name }}
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning">
                    <h3>آگهی‌های ویژه</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(\App\Models\Ad::where('is_premium', true)->latest()->take(4)->get() as $ad)
                            <div class="col-md-3 mb-3">
                                <div class="card h-100 border-warning">
                                    <div class="card-header bg-warning">
                                        <h5>
                                            <a href="{{ route('ads.show', $ad) }}">{{ $ad->title }}</a>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p>{{ Str::limit($ad->description, 100) }}</p>
                                        <p><strong>قیمت:</strong> {{ number_format($ad->price) }} تومان</p>
                                        <p><strong>دسته‌بندی:</strong> {{ $ad->category->name }}</p>
                                    </div>
                                    <div class="card-footer">
                                        <a href="{{ route('ads.show', $ad) }}" class="btn btn-sm btn-warning">مشاهده آگهی</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info">
                    <h3>آگهی‌های جدید</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(\App\Models\Ad::latest()->take(8)->get() as $ad)
                            <div class="col-md-3 mb-3">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5>
                                            <a href="{{ route('ads.show', $ad) }}">{{ $ad->title }}</a>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p>{{ Str::limit($ad->description, 100) }}</p>
                                        <p><strong>قیمت:</strong> {{ number_format($ad->price) }} تومان</p>
                                        <p><strong>دسته‌بندی:</strong> {{ $ad->category->name }}</p>
                                    </div>
                                    <div class="card-footer">
                                        <a href="{{ route('ads.show', $ad) }}" class="btn btn-sm btn-primary">مشاهده آگهی</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 text-center">
            <a href="{{ route('ads.index') }}" class="btn btn-lg btn-primary">مشاهده همه آگهی‌ها</a>
        </div>
    </div>
</div>
@endsection
