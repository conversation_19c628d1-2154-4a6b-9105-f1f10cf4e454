/* Admin Panel Styles */

:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;
}

body {
    font-family: 'Vazirmatn', 'Tahoma', sans-serif;
    background-color: #f8f9fc;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #4e73df 0%, #224abe 100%);
    color: white;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    transition: all 0.3s ease;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
}

.sidebar-user {
    padding: 1.5rem 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar-user img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    margin-left: 1rem;
}

.sidebar-user h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav .nav-item {
    position: relative;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-item.active .nav-link {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.sidebar-nav .nav-link i {
    margin-left: 0.75rem;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.admin-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: all 0.3s ease;
}

.admin-sidebar.collapsed + .admin-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* Cards */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

/* Stats Cards */
.stat-card {
    border-left: 4px solid;
    border-radius: 0.35rem;
}

.stat-card.primary {
    border-left-color: var(--primary-color);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.info {
    border-left-color: var(--info-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

.stat-card .card-body {
    padding: 1.25rem;
}

.stat-card .stat-icon {
    font-size: 2rem;
    opacity: 0.3;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
}

/* Forms */
.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Mobile Responsive */
@media (max-width: 767.98px) {
    .admin-sidebar {
        width: var(--sidebar-width);
        transform: translateX(100%);
    }
    
    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-right: 0;
    }
    
    .admin-sidebar.collapsed + .admin-content {
        margin-right: 0;
    }
}

/* Collapsed Sidebar */
@media (min-width: 768px) {
    .admin-sidebar.collapsed .sidebar-header h3,
    .admin-sidebar.collapsed .sidebar-user div,
    .admin-sidebar.collapsed .nav-link span {
        display: none;
    }
    
    .admin-sidebar.collapsed .sidebar-user {
        justify-content: center;
    }
    
    .admin-sidebar.collapsed .sidebar-user img {
        margin-left: 0;
    }
    
    .admin-sidebar.collapsed .nav-link {
        justify-content: center;
        padding: 1rem;
    }
    
    .admin-sidebar.collapsed .nav-link i {
        margin-left: 0;
        font-size: 1.25rem;
    }
}
